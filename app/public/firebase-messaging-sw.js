// public/firebase-messaging-sw.js

// Scripts for firebase and firebase messaging
importScripts('https://www.gstatic.com/firebasejs/9.15.0/firebase-app-compat.js'); // Use a recent, compatible version
importScripts('https://www.gstatic.com/firebasejs/9.15.0/firebase-messaging-compat.js');

// Initialize the Firebase app in the service worker
// Be sure to replace the config values below with your app's Firebase config.
// IMPORTANT: This configuration MUST match the one in your main app.
const firebaseConfig = {
  apiKey: "AIzaSyD3z86nxd9i4okjur85vpg4be-um8lQzn8", //  REPLACE WITH YOUR ACTUAL KEY
  authDomain: "dalti-3d06b.firebaseapp.com",      //  REPLACE WITH YOURS
  projectId: "dalti-3d06b",                       //  REPLACE WITH YOURS
  storageBucket: "dalti-3d06b.firebasestorage.app", //  REPLACE WITH YOURS
  messagingSenderId: "816987655237",              //  REPLACE WITH YOURS
  appId: "1:816987655237:web:99b535475db49b632e672c", //  REPLACE WITH YOURS
  // measurementId is not typically needed in the service worker
};

firebase.initializeApp(firebaseConfig);

// Retrieve an instance of Firebase Messaging so that it can handle background messages.
const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  // Customize notification here
  const notificationTitle = payload.notification.title || 'New Notification';
  const notificationOptions = {
    body: payload.notification.body || 'You have a new message.',
    icon: payload.notification.icon || '/logo.png', // Path to an icon in your public folder
    data: payload.data // Any custom data you send with the notification
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});