openapi: 3.0.3
info:
  title: Provider Mobile API
  description: |
    Comprehensive API for Provider Mobile Application - Dalti Platform

    This API provides complete business management functionality for service providers including:
    - Profile and account management
    - Location and service management
    - Queue/resource management
    - Schedule and appointment management
    - Customer relationship management
    - Reschedule request handling

    All endpoints require authentication via <PERSON><PERSON><PERSON> token in Authorization header.
    Base URL: https://api.dalti.app

    ## Important API Behavior Notes (Based on Integration Testing):

    ### Status Codes:
    - Validation errors return 500 status code instead of expected 400
    - Authentication errors may return 400 status code instead of expected 401
    - Foreign key constraint errors return 500 status code

    ### Response Format:
    - Location responses include: id, name, address, city, isMobileHidden, parking, elevator, handicapAccess
    - Location responses do NOT include: isActive, description, coordinates
    - Service responses include: id, title, duration, color, acceptOnline, acceptNew, notificationOn, pointsRequirements
    - Service responses do NOT include: description, price, isActive, categoryId in GET requests

    ### Required Fields:
    - Location creation requires: name, address, city, country, postalCode, latitude, longitude
    - Service creation requires: title, duration, categoryId
    - Queue creation requires: title, sProvidingPlaceId (locationId), serviceIds

    ### Dependencies:
    - Locations cannot be deleted if they have associated Opening records (foreign key constraint)
    - Services can be deleted without dependency checks (current behavior)
    - Queues cannot be deleted if they have future appointments
    
  version: 1.0.0
  contact:
    name: Dalti API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://dapi.adscloud.org
    description: Production server
  - url: https://dapi-test.adscloud.org:8443
    description: Development server

security:
  - BearerAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from authentication endpoints

  schemas:
    # Common Schemas
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates if the request was successful
        message:
          type: string
          description: Human-readable message
        data:
          type: object
          description: Response data (varies by endpoint)
      required:
        - success
        - message

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          description: Error message
        errors:
          type: object
          description: Validation errors (for 400 status codes)
          additionalProperties: true
        data:
          type: object
          description: Additional error data (optional)
      required:
        - success
        - message
      # Note: API returns 500 status codes for validation errors instead of 400
      # API returns 400 status codes for authentication errors instead of 401

    PaginationMeta:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
        page:
          type: integer
          description: Current page number
        limit:
          type: integer
          description: Items per page
        totalPages:
          type: integer
          description: Total number of pages
        hasNext:
          type: boolean
          description: Whether there are more pages
        hasPrev:
          type: boolean
          description: Whether there are previous pages

    # Provider Profile Schemas
    ProviderProfile:
      type: object
      properties:
        id:
          type: integer
          description: Provider ID
        businessName:
          type: string
          description: Business name
        description:
          type: string
          description: Business description
        categoryId:
          type: integer
          description: Provider category ID
        category:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
            description:
              type: string
        isSetupComplete:
          type: boolean
          description: Whether provider setup is complete
        isActive:
          type: boolean
          description: Whether provider is active
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    UpdateProviderProfileRequest:
      type: object
      properties:
        businessName:
          type: string
          minLength: 1
          maxLength: 255
        description:
          type: string
          maxLength: 1000
        categoryId:
          type: integer
          minimum: 1

    # Location Schemas
    Location:
      type: object
      properties:
        id:
          type: integer
          description: Location ID
        name:
          type: string
          description: Location name
        address:
          type: string
          description: Full formatted address string
        city:
          type: string
          description: City name
        isMobileHidden:
          type: boolean
          description: Whether location is hidden on mobile
        parking:
          type: boolean
          description: Whether parking is available
        elevator:
          type: boolean
          description: Whether elevator is available
        handicapAccess:
          type: boolean
          description: Whether handicap access is available
      required:
        - id
        - name
        - address
        - city
      # Note: API currently does not return isActive, description, coordinates fields

    Address:
      type: object
      properties:
        id:
          type: integer
        street:
          type: string
        city:
          type: string
        state:
          type: string
        postalCode:
          type: string
        country:
          type: string
        latitude:
          type: number
          format: double
        longitude:
          type: number
          format: double

    CreateLocationRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
          description: Location name
        description:
          type: string
          maxLength: 1000
          description: Location description (optional)
        address:
          type: string
          minLength: 1
          description: Full address string
        city:
          type: string
          minLength: 1
          description: City name
        country:
          type: string
          minLength: 1
          description: Country name
        postalCode:
          type: string
          minLength: 1
          description: Postal/ZIP code
        latitude:
          type: number
          format: double
          description: Latitude coordinate
        longitude:
          type: number
          format: double
          description: Longitude coordinate
        parking:
          type: boolean
          default: false
          description: Whether parking is available
        elevator:
          type: boolean
          default: false
          description: Whether elevator is available
        handicapAccess:
          type: boolean
          default: false
          description: Whether handicap access is available
      required:
        - name
        - address
        - city
        - country
        - postalCode
        - latitude
        - longitude

    UpdateLocationRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 255
        description:
          type: string
          maxLength: 1000
        isActive:
          type: boolean
        address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            postalCode:
              type: string
            country:
              type: string
            latitude:
              type: number
              format: double
            longitude:
              type: number
              format: double

    # Service Schemas
    Service:
      type: object
      properties:
        id:
          type: integer
          description: Service ID
        title:
          type: string
          description: Service title
        duration:
          type: integer
          description: Service duration in minutes
        color:
          type: string
          description: Service color (hex code)
        acceptOnline:
          type: boolean
          description: Whether service accepts online bookings
        acceptNew:
          type: boolean
          description: Whether service accepts new customers
        notificationOn:
          type: boolean
          description: Whether notifications are enabled
        pointsRequirements:
          type: integer
          description: Points required for service
      required:
        - id
        - title
        - duration
      # Note: API currently does not return description, price, isActive, categoryId fields in GET responses

    CreateServiceRequest:
      type: object
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
        description:
          type: string
          maxLength: 1000
        duration:
          type: integer
          minimum: 1
          maximum: 1440
        price:
          type: number
          format: double
          minimum: 0
        color:
          type: string
          pattern: '^#[0-9A-Fa-f]{6}$'
        categoryId:
          type: integer
          minimum: 1
      required:
        - title
        - duration
        - price
        - categoryId

    UpdateServiceRequest:
      type: object
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
        description:
          type: string
          maxLength: 1000
        duration:
          type: integer
          minimum: 1
          maximum: 1440
        price:
          type: number
          format: double
          minimum: 0
        color:
          type: string
          pattern: '^#[0-9A-Fa-f]{6}$'
        isActive:
          type: boolean
        categoryId:
          type: integer
          minimum: 1

    # Queue Schemas
    Queue:
      type: object
      properties:
        id:
          type: integer
          description: Queue ID
        title:
          type: string
          description: Queue title
        isActive:
          type: boolean
          description: Whether queue is active
        sProvidingPlaceId:
          type: integer
          description: Location ID where queue operates
        services:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              title:
                type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    CreateQueueRequest:
      type: object
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
        locationId:
          type: integer
          minimum: 1
        serviceIds:
          type: array
          items:
            type: integer
            minimum: 1
          minItems: 1
      required:
        - title
        - locationId
        - serviceIds

    UpdateQueueRequest:
      type: object
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
        isActive:
          type: boolean
        serviceIds:
          type: array
          items:
            type: integer
            minimum: 1
          minItems: 1

    # Schedule Schemas
    Schedule:
      type: object
      properties:
        id:
          type: integer
          description: Schedule ID
        dayOfWeek:
          type: integer
          description: Day of week (0=Sunday, 6=Saturday)
        startTime:
          type: string
          format: time
          description: Start time (HH:MM format)
        endTime:
          type: string
          format: time
          description: End time (HH:MM format)
        isActive:
          type: boolean
          description: Whether schedule is active
        locationId:
          type: integer
          description: Location ID
        location:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string

    CreateScheduleRequest:
      type: object
      properties:
        dayOfWeek:
          type: integer
          minimum: 0
          maximum: 6
        startTime:
          type: string
          pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
        endTime:
          type: string
          pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
        locationId:
          type: integer
          minimum: 1
      required:
        - dayOfWeek
        - startTime
        - endTime
        - locationId

    UpdateScheduleRequest:
      type: object
      properties:
        dayOfWeek:
          type: integer
          minimum: 0
          maximum: 6
        startTime:
          type: string
          pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
        endTime:
          type: string
          pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
        isActive:
          type: boolean
        locationId:
          type: integer
          minimum: 1

    # Customer Schemas
    Customer:
      type: object
      properties:
        id:
          type: string
          description: Customer ID
        firstName:
          type: string
          description: Customer first name
        lastName:
          type: string
          description: Customer last name
        email:
          type: string
          format: email
          description: Customer email
        phoneNumber:
          type: string
          description: Customer phone number
        isActive:
          type: boolean
          description: Whether customer is active
        totalAppointments:
          type: integer
          description: Total number of appointments
        lastAppointmentDate:
          type: string
          format: date-time
          description: Date of last appointment
        createdAt:
          type: string
          format: date-time

    CreateCustomerRequest:
      type: object
      properties:
        firstName:
          type: string
          minLength: 1
          maxLength: 100
        lastName:
          type: string
          minLength: 1
          maxLength: 100
        email:
          type: string
          format: email
        phoneNumber:
          type: string
          minLength: 10
          maxLength: 20
      required:
        - firstName
        - lastName
        - phoneNumber

    UpdateCustomerRequest:
      type: object
      properties:
        firstName:
          type: string
          minLength: 1
          maxLength: 100
        lastName:
          type: string
          minLength: 1
          maxLength: 100
        email:
          type: string
          format: email
        phoneNumber:
          type: string
          minLength: 10
          maxLength: 20
        isActive:
          type: boolean

    # Appointment Schemas
    Appointment:
      type: object
      properties:
        id:
          type: integer
          description: Appointment ID
        expectedAppointmentStartTime:
          type: string
          format: date-time
          description: Expected start time
        expectedAppointmentEndTime:
          type: string
          format: date-time
          description: Expected end time
        actualAppointmentStartTime:
          type: string
          format: date-time
          description: Actual start time
        actualAppointmentEndTime:
          type: string
          format: date-time
          description: Actual end time
        status:
          type: string
          enum: [pending, confirmed, in-progress, completed, canceled, noshow]
          description: Appointment status
        notes:
          type: string
          description: Appointment notes
        service:
          type: object
          properties:
            id:
              type: integer
            title:
              type: string
            duration:
              type: integer
            price:
              type: number
        customer:
          type: object
          properties:
            id:
              type: string
            firstName:
              type: string
            lastName:
              type: string
            phoneNumber:
              type: string
        queue:
          type: object
          properties:
            id:
              type: integer
            title:
              type: string
        createdAt:
          type: string
          format: date-time

    CreateAppointmentRequest:
      type: object
      properties:
        customerId:
          type: string
          minLength: 1
        serviceId:
          type: integer
          minimum: 1
        queueId:
          type: integer
          minimum: 1
        expectedStartTime:
          type: string
          format: date-time
        notes:
          type: string
          maxLength: 500
      required:
        - customerId
        - serviceId
        - queueId
        - expectedStartTime

    UpdateAppointmentRequest:
      type: object
      properties:
        expectedStartTime:
          type: string
          format: date-time
        expectedEndTime:
          type: string
          format: date-time
        actualStartTime:
          type: string
          format: date-time
        actualEndTime:
          type: string
          format: date-time
        notes:
          type: string
          maxLength: 500
        serviceId:
          type: integer
          minimum: 1
        queueId:
          type: integer
          minimum: 1

    UpdateAppointmentStatusRequest:
      type: object
      properties:
        status:
          type: string
          enum: [pending, confirmed, in-progress, completed, canceled, noshow]
        notes:
          type: string
          maxLength: 500
      required:
        - status

    # Reschedule Schemas
    RescheduleRequest:
      type: object
      properties:
        id:
          type: integer
          description: Reschedule request ID
        appointmentId:
          type: integer
          description: Original appointment ID
        requestedById:
          type: string
          description: ID of user who requested reschedule
        respondedById:
          type: string
          description: ID of user who responded to request
        suggestedStartTime:
          type: string
          format: date-time
          description: Suggested new start time
        suggestedEndTime:
          type: string
          format: date-time
          description: Suggested new end time
        reason:
          type: string
          description: Reason for reschedule
        status:
          type: string
          enum: [pending, accepted, rejected]
          description: Request status
        responseTime:
          type: string
          format: date-time
          description: Time when response was given
        responseNote:
          type: string
          description: Response note from provider
        createdAt:
          type: string
          format: date-time
        appointment:
          type: object
          properties:
            id:
              type: integer
            service:
              type: object
              properties:
                id:
                  type: integer
                title:
                  type: string
            customer:
              type: object
              properties:
                id:
                  type: string
                firstName:
                  type: string
                lastName:
                  type: string

    CreateRescheduleRequest:
      type: object
      properties:
        newStartTime:
          type: string
          format: date-time
        reason:
          type: string
          maxLength: 500
        notifyCustomer:
          type: boolean
          default: true
      required:
        - newStartTime

    RespondToRescheduleRequest:
      type: object
      properties:
        response:
          type: string
          enum: [approve, reject]
        notes:
          type: string
          maxLength: 500
        notifyCustomer:
          type: boolean
          default: true
      required:
        - response

paths:
  # Authentication Endpoints
  /api/auth/request-email-otp:
    post:
      tags:
        - Authentication
      summary: Request email OTP for provider registration
      description: Request an OTP to be sent to the provider's email for registration
      security: []  # No authentication required
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: Provider's email address
                firstName:
                  type: string
                  description: Provider's first name
                lastName:
                  type: string
                  description: Provider's last name
                password:
                  type: string
                  minLength: 8
                  description: Provider's password
                isProviderRegistration:
                  type: boolean
                  description: Must be true for provider registration
                providerCategoryId:
                  type: integer
                  description: Provider category ID
                businessName:
                  type: string
                  description: Business name
                phone:
                  type: string
                  description: Provider's phone number
              required:
                - email
                - firstName
                - lastName
                - password
                - isProviderRegistration
                - providerCategoryId
                - businessName
                - phone
      responses:
        '200':
          description: OTP sent successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      message:
                        type: string
                        example: "OTP sent successfully"
                      providerContext:
                        type: object
                        properties:
                          isProviderRegistration:
                            type: boolean
                          providerCategoryId:
                            type: integer
                      otp:
                        type: string
                        description: "OTP code (only in test mode)"
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/auth/request-otp:
    post:
      tags:
        - Authentication
      summary: Request phone OTP for provider registration
      description: Request an OTP to be sent to the provider's phone for registration
      security: []  # No authentication required
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                phoneNumber:
                  type: string
                  description: Provider's phone number
                firstName:
                  type: string
                  description: Provider's first name
                lastName:
                  type: string
                  description: Provider's last name
                isProviderRegistration:
                  type: boolean
                  description: Must be true for provider registration
                providerCategoryId:
                  type: integer
                  description: Provider category ID
                businessName:
                  type: string
                  description: Business name
              required:
                - phoneNumber
                - firstName
                - lastName
                - isProviderRegistration
                - providerCategoryId
                - businessName
      responses:
        '200':
          description: OTP sent successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      message:
                        type: string
                        example: "OTP sent successfully"
                      providerContext:
                        type: object
                        properties:
                          isProviderRegistration:
                            type: boolean
                          providerCategoryId:
                            type: integer
                      otp:
                        type: string
                        description: "OTP code (only in test mode)"
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Failed to send OTP (SMS service not configured)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/auth/provider/verify-otp-register:
    post:
      tags:
        - Authentication
      summary: Complete provider registration with OTP verification
      description: Verify OTP and complete provider registration
      security: []  # No authentication required
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                otp:
                  type: string
                  description: OTP code received via email or SMS
                identifier:
                  type: string
                  description: Email or phone number used for OTP request
                password:
                  type: string
                  description: Provider's password
                firstName:
                  type: string
                  description: Provider's first name
                lastName:
                  type: string
                  description: Provider's last name
                providerCategoryId:
                  type: integer
                  description: Provider category ID
                businessName:
                  type: string
                  description: Business name
                phone:
                  type: string
                  description: Provider's phone number
                email:
                  type: string
                  format: email
                  description: Provider's email address
              required:
                - otp
                - identifier
                - password
                - firstName
                - lastName
                - providerCategoryId
                - businessName
                - phone
                - email
      responses:
        '201':
          description: Provider registered successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      sessionId:
                        type: string
                        description: JWT session token
                      user:
                        type: object
                        properties:
                          id:
                            type: integer
                          email:
                            type: string
                          firstName:
                            type: string
                          lastName:
                            type: string
                          role:
                            type: string
                            example: "CUSTOMER"
                      provider:
                        type: object
                        properties:
                          id:
                            type: integer
                          userId:
                            type: integer
                          providerCategoryId:
                            type: integer
                          title:
                            type: string
                          isSetupComplete:
                            type: boolean
        '400':
          description: Invalid OTP or request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/auth/provider/login:
    post:
      tags:
        - Authentication
      summary: Provider login
      description: Authenticate provider with email/phone and password
      security: []  # No authentication required
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                identifier:
                  type: string
                  description: Email or phone number
                password:
                  type: string
                  description: Provider's password
              required:
                - identifier
                - password
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      sessionId:
                        type: string
                        description: JWT session token
                      user:
                        type: object
                        properties:
                          id:
                            type: integer
                          email:
                            type: string
                          firstName:
                            type: string
                          lastName:
                            type: string
                          role:
                            type: string
                      provider:
                        type: object
                        properties:
                          id:
                            type: integer
                          userId:
                            type: integer
                          providerCategoryId:
                            type: integer
                          title:
                            type: string
                          isSetupComplete:
                            type: boolean
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/auth/password-reset-request:
    post:
      tags:
        - Authentication
      summary: Request password reset
      description: Request a password reset for provider account
      security: []  # No authentication required
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: Provider's email address
              required:
                - email
      responses:
        '200':
          description: Password reset request sent
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: Endpoint not implemented
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/auth/refresh-token:
    post:
      tags:
        - Authentication
      summary: Refresh authentication token
      description: Refresh the JWT token for continued authentication
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      sessionId:
                        type: string
                        description: New JWT session token
        '401':
          description: Invalid or expired token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Endpoint not implemented
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Provider Profile Management
  /api/auth/providers/onboarding/status:
    get:
      tags:
        - Provider Profile
      summary: Get provider onboarding status
      description: Check if the provider needs to complete onboarding setup
      responses:
        '200':
          description: Onboarding status retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          needsOnboarding:
                            type: boolean
                            description: True if provider needs to complete onboarding, false if setup is complete
                        required:
                          - needsOnboarding
              examples:
                needs_onboarding:
                  summary: Provider needs onboarding
                  value:
                    success: true
                    message: "Onboarding status retrieved successfully"
                    data:
                      needsOnboarding: true
                setup_complete:
                  summary: Provider setup complete
                  value:
                    success: true
                    message: "Onboarding status retrieved successfully"
                    data:
                      needsOnboarding: false
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Provider profile not found
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ErrorResponse'
                  - type: object
                    properties:
                      message:
                        example: "Provider profile not found for this user. Please register as a provider first."

  /api/auth/providers/profile:
    get:
      tags:
        - Provider Profile
      summary: Get provider profile
      description: Retrieve the authenticated provider's profile information
      responses:
        '200':
          description: Provider profile retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ProviderProfile'
        '401':
          description: Unauthorized - Invalid or missing authentication token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Provider profile not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - Provider Profile
      summary: Update provider profile
      description: Update the authenticated provider's profile information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProviderProfileRequest'
      responses:
        '200':
          description: Provider profile updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ProviderProfile'
        '400':
          description: Bad request - Invalid input data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Location Management
  /api/auth/providers/locations:
    get:
      tags:
        - Location Management
      summary: Get provider locations
      description: Retrieve all locations for the authenticated provider
      parameters:
        - name: isActive
          in: query
          description: Filter by active status
          required: false
          schema:
            type: boolean
        - name: search
          in: query
          description: Search locations by name
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Locations retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Location'

    post:
      tags:
        - Location Management
      summary: Create new location
      description: Create a new location for the authenticated provider
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateLocationRequest'
      responses:
        '201':
          description: Location created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Location'

  /api/auth/providers/locations/{id}:
    put:
      tags:
        - Location Management
      summary: Update location
      description: Update an existing location
      parameters:
        - name: id
          in: path
          required: true
          description: Location ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateLocationRequest'
      responses:
        '200':
          description: Location updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Location'

    delete:
      tags:
        - Location Management
      summary: Delete location
      description: Delete a location (only if no active queues or future appointments)
      parameters:
        - name: id
          in: path
          required: true
          description: Location ID
          schema:
            type: integer
      responses:
        '200':
          description: Location deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '409':
          description: Conflict - Location has dependencies
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Service Management
  /api/auth/providers/services:
    get:
      tags:
        - Service Management
      summary: Get provider services
      description: Retrieve all services for the authenticated provider
      parameters:
        - name: isActive
          in: query
          description: Filter by active status
          required: false
          schema:
            type: boolean
        - name: categoryId
          in: query
          description: Filter by category ID
          required: false
          schema:
            type: integer
        - name: search
          in: query
          description: Search services by title
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Services retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Service'

    post:
      tags:
        - Service Management
      summary: Create new service
      description: Create a new service for the authenticated provider
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateServiceRequest'
      responses:
        '201':
          description: Service created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Service'

  /api/auth/providers/services/{id}:
    put:
      tags:
        - Service Management
      summary: Update service
      description: Update an existing service
      parameters:
        - name: id
          in: path
          required: true
          description: Service ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateServiceRequest'
      responses:
        '200':
          description: Service updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Service'

    delete:
      tags:
        - Service Management
      summary: Delete service
      description: Delete a service (only if no active appointments or queue assignments)
      parameters:
        - name: id
          in: path
          required: true
          description: Service ID
          schema:
            type: integer
      responses:
        '200':
          description: Service deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '409':
          description: Conflict - Service has dependencies
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Queue Management
  /api/auth/providers/queues:
    get:
      tags:
        - Queue Management
      summary: Get provider queues
      description: Retrieve all queues for the authenticated provider
      parameters:
        - name: isActive
          in: query
          description: Filter by active status
          required: false
          schema:
            type: boolean
        - name: locationId
          in: query
          description: Filter by location ID
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Queues retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Queue'

    post:
      tags:
        - Queue Management
      summary: Create new queue
      description: Create a new queue for the authenticated provider
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateQueueRequest'
      responses:
        '201':
          description: Queue created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Queue'

  /api/auth/providers/queues/{id}:
    put:
      tags:
        - Queue Management
      summary: Update queue
      description: Update an existing queue
      parameters:
        - name: id
          in: path
          required: true
          description: Queue ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateQueueRequest'
      responses:
        '200':
          description: Queue updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Queue'

    delete:
      tags:
        - Queue Management
      summary: Delete queue
      description: Delete a queue (only if no future appointments)
      parameters:
        - name: id
          in: path
          required: true
          description: Queue ID
          schema:
            type: integer
      responses:
        '200':
          description: Queue deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '409':
          description: Conflict - Queue has future appointments
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/auth/providers/locations/{locationId}/queues:
    get:
      tags:
        - Queue Management
      summary: Get queues by location
      description: Retrieve all queues for a specific location
      parameters:
        - name: locationId
          in: path
          required: true
          description: Location ID
          schema:
            type: integer
        - name: isActive
          in: query
          description: Filter by active status
          required: false
          schema:
            type: boolean
      responses:
        '200':
          description: Location queues retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Queue'

  # Queue Service Assignment
  /api/auth/providers/queues/{queueId}/services:
    get:
      tags:
        - Queue Management
      summary: Get queue services
      description: Get all services assigned to a specific queue
      parameters:
        - name: queueId
          in: path
          required: true
          description: Queue ID
          schema:
            type: integer
      responses:
        '200':
          description: Queue services retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            queueId:
                              type: integer
                            serviceId:
                              type: integer
                            service:
                              $ref: '#/components/schemas/Service'

    post:
      tags:
        - Queue Management
      summary: Assign service to queue
      description: Assign a service to a queue
      parameters:
        - name: queueId
          in: path
          required: true
          description: Queue ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                serviceId:
                  type: integer
                  minimum: 1
              required:
                - serviceId
      responses:
        '201':
          description: Service assigned to queue successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          queueId:
                            type: integer
                          serviceId:
                            type: integer
                          service:
                            $ref: '#/components/schemas/Service'

  /api/auth/providers/queues/{queueId}/services/{serviceId}:
    delete:
      tags:
        - Queue Management
      summary: Remove service from queue
      description: Remove a service assignment from a queue
      parameters:
        - name: queueId
          in: path
          required: true
          description: Queue ID
          schema:
            type: integer
        - name: serviceId
          in: path
          required: true
          description: Service ID
          schema:
            type: integer
      responses:
        '200':
          description: Service removed from queue successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '409':
          description: Cannot remove last service from queue
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Schedule Management
  /api/auth/providers/schedules:
    get:
      tags:
        - Schedule Management
      summary: Get provider schedules
      description: Retrieve all schedules for the authenticated provider
      parameters:
        - name: locationId
          in: query
          description: Filter by location ID
          required: false
          schema:
            type: integer
        - name: dayOfWeek
          in: query
          description: Filter by day of week (0=Sunday, 6=Saturday)
          required: false
          schema:
            type: integer
            minimum: 0
            maximum: 6
        - name: isActive
          in: query
          description: Filter by active status
          required: false
          schema:
            type: boolean
      responses:
        '200':
          description: Schedules retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Schedule'

    post:
      tags:
        - Schedule Management
      summary: Create new schedule
      description: Create a new schedule for the authenticated provider
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateScheduleRequest'
      responses:
        '201':
          description: Schedule created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Schedule'

  /api/auth/providers/schedules/{id}:
    put:
      tags:
        - Schedule Management
      summary: Update schedule
      description: Update an existing schedule
      parameters:
        - name: id
          in: path
          required: true
          description: Schedule ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateScheduleRequest'
      responses:
        '200':
          description: Schedule updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Schedule'

    delete:
      tags:
        - Schedule Management
      summary: Delete schedule
      description: Delete a schedule
      parameters:
        - name: id
          in: path
          required: true
          description: Schedule ID
          schema:
            type: integer
      responses:
        '200':
          description: Schedule deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # Customer Management
  /api/auth/providers/customers:
    get:
      tags:
        - Customer Management
      summary: Get provider customers
      description: Retrieve all customers for the authenticated provider
      parameters:
        - name: search
          in: query
          description: Search customers by name, email, or phone
          required: false
          schema:
            type: string
        - name: isActive
          in: query
          description: Filter by active status
          required: false
          schema:
            type: boolean
        - name: page
          in: query
          description: Page number for pagination
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          customers:
                            type: array
                            items:
                              $ref: '#/components/schemas/Customer'
                          pagination:
                            $ref: '#/components/schemas/PaginationMeta'

    post:
      tags:
        - Customer Management
      summary: Create new customer
      description: Create a new customer for the authenticated provider
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Customer'

  /api/auth/providers/customers/{id}:
    get:
      tags:
        - Customer Management
      summary: Get customer details
      description: Get detailed information about a specific customer
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: string
      responses:
        '200':
          description: Customer details retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        allOf:
                          - $ref: '#/components/schemas/Customer'
                          - type: object
                            properties:
                              recentAppointments:
                                type: array
                                items:
                                  $ref: '#/components/schemas/Appointment'

    put:
      tags:
        - Customer Management
      summary: Update customer
      description: Update an existing customer
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomerRequest'
      responses:
        '200':
          description: Customer updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Customer'

  # Appointment Management
  /api/auth/providers/appointments:
    get:
      tags:
        - Appointment Management
      summary: Get provider appointments
      description: Retrieve appointments for the authenticated provider
      parameters:
        - name: status
          in: query
          description: Filter by appointment status
          required: false
          schema:
            type: string
            enum: [pending, confirmed, in-progress, completed, canceled, noshow]
        - name: from
          in: query
          description: Filter appointments from this date (ISO 8601)
          required: false
          schema:
            type: string
            format: date
        - name: to
          in: query
          description: Filter appointments to this date (ISO 8601)
          required: false
          schema:
            type: string
            format: date
        - name: customerId
          in: query
          description: Filter by customer ID
          required: false
          schema:
            type: string
        - name: serviceId
          in: query
          description: Filter by service ID
          required: false
          schema:
            type: integer
        - name: queueId
          in: query
          description: Filter by queue ID
          required: false
          schema:
            type: integer
        - name: page
          in: query
          description: Page number for pagination
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Appointments retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          appointments:
                            type: array
                            items:
                              $ref: '#/components/schemas/Appointment'
                          pagination:
                            $ref: '#/components/schemas/PaginationMeta'

    post:
      tags:
        - Appointment Management
      summary: Create new appointment
      description: Create a new appointment for the authenticated provider
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAppointmentRequest'
      responses:
        '201':
          description: Appointment created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Appointment'

  /api/auth/providers/appointments/{id}:
    put:
      tags:
        - Appointment Management
      summary: Update appointment
      description: Update an existing appointment
      parameters:
        - name: id
          in: path
          required: true
          description: Appointment ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAppointmentRequest'
      responses:
        '200':
          description: Appointment updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Appointment'

  /api/auth/providers/appointments/{id}/status:
    put:
      tags:
        - Appointment Management
      summary: Update appointment status
      description: Update the status of an existing appointment
      parameters:
        - name: id
          in: path
          required: true
          description: Appointment ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAppointmentStatusRequest'
      responses:
        '200':
          description: Appointment status updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Appointment'

  # Reschedule Management
  /api/auth/providers/reschedules:
    get:
      tags:
        - Reschedule Management
      summary: Get provider reschedule requests
      description: Retrieve reschedule requests for the authenticated provider
      parameters:
        - name: status
          in: query
          description: Filter by reschedule status
          required: false
          schema:
            type: string
            enum: [pending, accepted, rejected, all]
            default: pending
        - name: from
          in: query
          description: Filter requests from this date (ISO 8601)
          required: false
          schema:
            type: string
            format: date
        - name: to
          in: query
          description: Filter requests to this date (ISO 8601)
          required: false
          schema:
            type: string
            format: date
        - name: page
          in: query
          description: Page number for pagination
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Reschedule requests retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          data:
                            type: array
                            items:
                              $ref: '#/components/schemas/RescheduleRequest'
                          pagination:
                            $ref: '#/components/schemas/PaginationMeta'

  /api/auth/providers/reschedules/{id}:
    get:
      tags:
        - Reschedule Management
      summary: Get reschedule request details
      description: Get detailed information about a specific reschedule request
      parameters:
        - name: id
          in: path
          required: true
          description: Reschedule request ID
          schema:
            type: integer
      responses:
        '200':
          description: Reschedule request details retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/RescheduleRequest'

  /api/auth/providers/appointments/{id}/reschedule:
    post:
      tags:
        - Reschedule Management
      summary: Create reschedule request
      description: Create a new reschedule request for an appointment
      parameters:
        - name: id
          in: path
          required: true
          description: Appointment ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRescheduleRequest'
      responses:
        '201':
          description: Reschedule request created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          id:
                            type: integer
                          appointmentId:
                            type: integer
                          suggestedStartTime:
                            type: string
                            format: date-time
                          suggestedEndTime:
                            type: string
                            format: date-time
                          reason:
                            type: string
                          status:
                            type: string
                          createdAt:
                            type: string
                            format: date-time

  /api/auth/providers/reschedules/{id}/respond:
    put:
      tags:
        - Reschedule Management
      summary: Respond to reschedule request
      description: Approve or reject a reschedule request
      parameters:
        - name: id
          in: path
          required: true
          description: Reschedule request ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RespondToRescheduleRequest'
      responses:
        '200':
          description: Reschedule request response recorded successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          id:
                            type: integer
                          status:
                            type: string
                          responseTime:
                            type: string
                            format: date-time
                          responseNote:
                            type: string

tags:
  - name: Authentication
    description: Provider authentication and registration operations
  - name: Provider Profile
    description: Provider profile management operations
  - name: Location Management
    description: Provider location management operations
  - name: Service Management
    description: Provider service management operations
  - name: Queue Management
    description: Provider queue/resource management operations
  - name: Schedule Management
    description: Provider schedule management operations
  - name: Customer Management
    description: Provider customer management operations
  - name: Appointment Management
    description: Provider appointment management operations
  - name: Reschedule Management
    description: Appointment reschedule request management operations
