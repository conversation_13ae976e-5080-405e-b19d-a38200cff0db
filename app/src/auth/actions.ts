import { HttpError, prisma } from 'wasp/server';
import { type User, type SProvider } from 'wasp/entities';
import type {
    RequestPhoneOtp,
    CreateSProviderForUser,
    CreateCustomerUser,
    VerifyOtpAndRegister,
    LoginWithPhoneOrEmail
} from 'wasp/server/operations';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation';
import { z } from 'zod';
import { verifyPassword } from 'wasp/auth/password'
import {
    createEmailVerificationLink,
    sendEmailVerificationEmail,
    createUser,
    createProviderId,
    sanitizeAndSerializeProviderData,
    updateAuthIdentityProviderData,
    findAuthIdentity,
    getProviderDataWithPassword,
} from 'wasp/server/auth';
import { createInvalidCredentialsError } from 'wasp/auth/utils'
import { createSession } from 'wasp/auth/session'
import { Role, Prisma } from '@prisma/client';
import { sendOtpSms } from '../server/smsService';

/**
 * Helper function to check if OTP test mode is enabled
 * Test mode allows returning the generated OTP code in the response for testing purposes
 * Only enabled in development/test environments for security
 */
function isOtpTestModeEnabled(): boolean {
  const testModeEnabled = process.env.OTP_TEST_MODE === 'true';
  const isProduction = process.env.NODE_ENV === 'production';

  if (testModeEnabled && !isProduction) {
    console.warn('[OTP_TEST_MODE] Test mode is enabled - OTP codes will be returned in API responses');
    return true;
  }

  if (testModeEnabled && isProduction) {
    console.error('[OTP_TEST_MODE] Test mode is disabled in production for security reasons');
  }

  return false;
}
import bcrypt from 'bcryptjs';

// Define input type for the new action


const createSProviderInputSchema = z.object({
  providerCategoryId: z.number(),
  email: z.string(),
});

type CreateSProviderData = z.infer<typeof createSProviderInputSchema>

export const createSProviderForUser: CreateSProviderForUser<CreateSProviderData, SProvider> = async (
    rawArgs,
    context
  ) => {
    const { providerCategoryId, email } = ensureArgsSchemaOrThrowHttpError(createSProviderInputSchema, rawArgs);
    if(!context.entities.User) {
        throw new HttpError(500, 'User entity not found');
    }
    
    const user = await context.entities.User.findFirst({
        where: {
            email: email,
            role: 'CLIENT',
        },
    });

    if(!user) {
        throw new HttpError(500, 'User not found'); 
    }
    
    const userId = user?.id;

    
    
    // Check if user already has a provider profile
    const existingProvider = await context.entities.SProvider.findUnique({
        where: { userId: userId }
    });

    if (existingProvider) {
        throw new HttpError(400, 'User already has a service provider profile.');
    }

    // Create the SProvider record
    try {
        const newProvider = await context.entities.SProvider.create({
            data: {
                userId: userId,
                providerCategoryId: providerCategoryId,
                // Add any other default fields for SProvider if necessary
            },
        });
        return newProvider;
    } catch (error: any) {
    console.error(`Failed to create SProvider for user ${userId}:`, error);
    // Check for specific Prisma errors if needed (e.g., foreign key constraint)
    if (error.code === 'P2003') { // Foreign key constraint failed
        throw new HttpError(400, 'Invalid Provider Category ID.');
    }
    throw new HttpError(500, error.message || 'Failed to create provider profile.');
    }
}

// --- Create Customer User Action ---

const createCustomerUserInputSchema = z.object({
    email: z.string().email("Invalid email format"),
    password: z.string().min(8, "Password must be at least 8 characters long"),
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
});

type CreateCustomerUserData = z.infer<typeof createCustomerUserInputSchema>;

export const createCustomerUser: CreateCustomerUser<CreateCustomerUserData, User> = async (
    rawArgs,
    context
): Promise<User> => {
    const args = ensureArgsSchemaOrThrowHttpError(createCustomerUserInputSchema, rawArgs);

    let userToReturn: User | null = null;

    try {
        // Fetch existing user including auth identities
        const existingUser = await context.entities.User.findUnique({
            where: { email: args.email },
            include: { auth: { include: { identities: true } } }
        });

        // Prepare auth data (needed for both create and update)
        const providerId = createProviderId('email', args.email);
        const providerData = await sanitizeAndSerializeProviderData({ hashedPassword: args.password });

        if (existingUser) {
            // Check if user already has an email/password identity
            const hasEmailAuth = existingUser.auth?.identities?.some((identity: { providerName: string; providerUserId: string }) => identity.providerName === 'email');

            if (hasEmailAuth) {
                // User exists and already has password login setup
                throw new HttpError(409, 'An account with this email already exists and has login credentials. Please log in or use the "Forgot Password" option.');
            } else {
                // User exists but lacks email/password identity -> Add it
                console.log(`User ${existingUser.email} found, adding email auth identity.`);
                const updatedUser = await prisma.$transaction(async (tx) => {
                    // Ensure Auth record exists
                    const auth = await tx.auth.upsert({
                        where: { userId: existingUser.id },
                        update: {},
                        create: { userId: existingUser.id },
                    });

                    // Create the AuthIdentity
                    await tx.authIdentity.create({
                        data: {
                            authId: auth.id,
                            providerName: 'email',
                            providerUserId: args.email, // Using email as providerUserId for 'email' provider
                            providerData: providerData,
                        }
                    });

                    // Optionally update first/last name if provided and different/missing
                    // For now, we just ensure the user record exists and return it
                    // Re-fetch user within transaction to ensure atomicity and get latest data
                    return tx.user.findUniqueOrThrow({
                        where: { id: existingUser.id },
                        // Include necessary fields for email verification if needed later
                        // select: { id: true, email: true, firstName: true }
                    });
                });
                userToReturn = updatedUser;
                console.log(`Email auth added successfully for user ${userToReturn.email}`);
            }
        } else {
            // User does not exist -> Create new user
            console.log(`No existing user found for ${args.email}, creating new user.`);
            const additionalUserData = {
                firstName: args.firstName,
                lastName: args.lastName,
                role: Role.CUSTOMER,
            };

            const newUser = await createUser(
                providerId,
                providerData,
                {
                    ...additionalUserData,
                    email: args.email
                }
            );
            userToReturn = newUser;
            console.log(`New user created successfully: ${userToReturn.email}`);
        }

        // --- Email Verification (runs for both new users and existing users getting auth) ---
        if (userToReturn?.email) {
            const emailVerificationClientRoute = '/email-verification';
            const verificationLink = await createEmailVerificationLink(userToReturn.email, emailVerificationClientRoute);
            try {
                 await sendEmailVerificationEmail(
                     userToReturn.email,
                     {
                         to: userToReturn.email,
                         subject: "Verify your email for Dalti",
                         text: `Click the link below to verify your email: ${verificationLink}`,
                         html:
                           `<p>Hi ${userToReturn.firstName || 'there'},</p> // Use firstName if available
                            <p>Please click the link below to verify your email address for your Dalti account:</p>
                            <a href="${verificationLink}">Verify Email</a>
                            <p>Thanks,<br/>The Dalti Team</p>`,
                     }
                 );
                 console.log(`Verification email sent to ${userToReturn.email}`);
            } catch (verificationEmailError: any) {
                // Log error but don't fail the whole operation just because email failed
                console.error(`Failed to send verification email to ${userToReturn.email}:`, verificationEmailError);
            }
        } else {
             console.warn("User processed, but email was missing or user object unavailable. Skipping verification email sending.");
        }

    } catch (error: any) {
        console.error("Error during customer user creation/update or verification sending:", error);
        if (error instanceof HttpError) {
            throw error; // Re-throw known HttpErrors
        }
        // Handle potential Prisma unique constraint violations (e.g., during transaction)
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
            if (error.code === 'P2002') { // Unique constraint violation
                 const target = error.meta?.target as string[] | undefined;
                 if (target?.includes('email') || target?.includes('User_email_key')) {
                     throw new HttpError(409, "A user with this email already exists (concurrent creation?).");
                 } else if (target?.includes('AuthIdentity_providerName_providerUserId_key')) {
                     // This might happen if the transaction runs concurrently after initial check
                     throw new HttpError(409, "Auth identity conflict for this email (concurrent update?). Please try again or log in.");
                 } else {
                     throw new HttpError(409, "A conflict occurred due to duplicate data.");
                 }
            }
        }
        // Fallback for other errors
        throw new HttpError(500, error.message || 'An unexpected error occurred during user processing.');
    }

    // Ensure we always return a user if the process succeeded
    if (!userToReturn) {
        // This should ideally not happen if logic is correct, but acts as a safeguard
        console.error("User creation/update process completed, but resulting user data is unexpectedly unavailable.");
        throw new HttpError(500, "User processing completed, but user data is unexpectedly unavailable.");
    }

    return userToReturn;
};

// --- Request Phone OTP Action ---

const requestPhoneOtpInputSchema = z.object({
  phoneNumber: z.string().min(1, "Phone number is required"),
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
});

export type RequestPhoneOtpData = z.infer<typeof requestPhoneOtpInputSchema>;

export const requestPhoneOtp: RequestPhoneOtp<RequestPhoneOtpData, { message: string }> = async (
  rawArgs,
  context
) => {
  const args = ensureArgsSchemaOrThrowHttpError(requestPhoneOtpInputSchema, rawArgs);

  if (!context.entities.User) {
    throw new HttpError(500, 'User entity not configured for this action.');
  }

  const COOLDOWN_PERIOD_MS = 60 * 1000; // 60 seconds

  // 1. Check for existing user and cooldown
  let existingUser = await context.entities.User.findUnique({
    where: { mobileNumber: args.phoneNumber },
  });

  if (existingUser && existingUser.lastOtpSentAt) {
    const timeSinceLastOtp = Date.now() - new Date(existingUser.lastOtpSentAt).getTime();
    if (timeSinceLastOtp < COOLDOWN_PERIOD_MS) {
      throw new HttpError(429, "Please wait before requesting another OTP.");
    }
  }

  // 2. If user exists and is already verified, they should log in
  if (existingUser && existingUser.isPhoneVerified) {
    throw new HttpError(409, "This phone number is already verified. Please log in.");
  }

  // 3. Generate OTP and expiry
  const otp = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // OTP expires in 10 minutes
  const now = new Date();

  let userForSms: Pick<User, 'mobileNumber' | 'firstName'>;

  // 4. Create or Update user
  if (existingUser) {
    // User exists but is not verified, update OTP
    const updatedUser = await context.entities.User.update({
      where: { id: existingUser.id },
      data: {
        phoneOtp: otp,
        phoneOtpExpiresAt: otpExpiresAt,
        lastOtpSentAt: now,
        // Optionally update firstName/lastName if provided and different/missing
        ...(args.firstName && { firstName: args.firstName }),
        ...(args.lastName && { lastName: args.lastName }),
      },
    });
    userForSms = { mobileNumber: updatedUser.mobileNumber!, firstName: updatedUser.firstName };
  } else {
    // User does not exist, create new user
    const newUser = await context.entities.User.create({
      data: {
        mobileNumber: args.phoneNumber,
        firstName: args.firstName,
        lastName: args.lastName,
        role: Role.CUSTOMER, // Default role for new OTP signups
        phoneOtp: otp,
        phoneOtpExpiresAt: otpExpiresAt,
        isPhoneVerified: false,
        lastOtpSentAt: now,
        // You might want to set other default fields for the User entity here
      },
    });
    userForSms = { mobileNumber: newUser.mobileNumber!, firstName: newUser.firstName };
  }
  
  if (!userForSms.mobileNumber) {
      throw new HttpError(500, "User phone number is unexpectedly missing after create/update.");
  }

  // 5. Send OTP SMS
  const smsSentSuccessfully = await sendOtpSms(userForSms.mobileNumber, otp);

  if (!smsSentSuccessfully) {
    // Potentially roll back user creation/update or log for manual review
    // For now, we'll throw an error, which means the user might exist with an OTP that wasn't sent.
    throw new HttpError(500, "Failed to send OTP. Please try again.");
  }

  return { message: "OTP sent successfully to your phone number. Please verify to complete registration." };
};

// --- Verify OTP and Register Action ---

const verifyOtpAndRegisterInputSchema = z.object({
  otp: z.string().length(6, "OTP must be 6 digits"),
  identifier: z.string().min(1, "Identifier (email or phone) is required"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
  email: z.string().email("Invalid email format").optional(),
});

export type VerifyOtpAndRegisterData = z.infer<typeof verifyOtpAndRegisterInputSchema>;

// UserWithAuthAndIdentities type is already defined above for loginWithPhoneOrEmail and can be reused here.
export const verifyOtpAndRegister: VerifyOtpAndRegister<VerifyOtpAndRegisterData, User> = async (rawArgs: any, context: any) => {
  console.log("[verifyOtpAndRegister] Start function");
  const args = ensureArgsSchemaOrThrowHttpError(verifyOtpAndRegisterInputSchema, rawArgs);
  console.log("[verifyOtpAndRegister] args:", args);

  if (!context.entities.User) {
    console.log("[verifyOtpAndRegister] context.entities.User is missing");
    throw new HttpError(500, 'Required entities (User) not configured for this action.');
  }

  const isEmailIdentifier = args.identifier.includes('@');
  console.log("[verifyOtpAndRegister] isEmailIdentifier:", isEmailIdentifier);

  let user: UserWithAuthAndIdentities | null = null;

  if (isEmailIdentifier) {
    console.log("[verifyOtpAndRegister] Looking up user by email:", args.identifier);
    user = await context.entities.User.findUnique({
      where: { email: args.identifier },
      include: { auth: { include: { identities: true } } }
    });
  } else {
    console.log("[verifyOtpAndRegister] Looking up user by mobileNumber:", args.identifier);
    user = await context.entities.User.findUnique({
      where: { mobileNumber: args.identifier },
      include: { auth: { include: { identities: true } } }
    });
  }

  console.log("[verifyOtpAndRegister] user found:", !!user, user && { id: user.id, email: user.email, mobileNumber: user.mobileNumber });

  if (!user) {
    console.log("[verifyOtpAndRegister] No user found for identifier");
    throw new HttpError(404, "User not found with this identifier. Please request an OTP first.");
  }

  let otpField: 'phoneOtp' | 'emailOtp';
  let otpExpiresAtField: 'phoneOtpExpiresAt' | 'emailOtpExpiresAt';
  let isVerifiedField: 'isPhoneVerified' | 'isEmailVerified';

  if (isEmailIdentifier) {
    console.log("[verifyOtpAndRegister] Identifier is email, checking user.email match");
    if (user.email !== args.identifier) {
      console.log("[verifyOtpAndRegister] Identifier mismatch for email verification. user.email:", user.email, "args.identifier:", args.identifier);
      throw new HttpError(400, "Identifier mismatch for email verification.");
    }
    otpField = 'emailOtp';
    otpExpiresAtField = 'emailOtpExpiresAt';
    isVerifiedField = 'isEmailVerified';
    console.log("[verifyOtpAndRegister] Using email OTP fields");
    if (user.isEmailVerified) {
      console.log("[verifyOtpAndRegister] User is already email verified, checking for password auth");
      const hasPasswordAuth = user.auth?.identities?.some((identity) => identity.providerName === 'email');
      console.log("[verifyOtpAndRegister] hasPasswordAuth:", hasPasswordAuth);
      if (hasPasswordAuth) {
        console.log("[verifyOtpAndRegister] Email already verified and has password, throwing error");
        throw new HttpError(409, "This email is already verified and has a password. Please log in.");
      }
    }
  } else { // Phone Identifier
    otpField = 'phoneOtp';
    otpExpiresAtField = 'phoneOtpExpiresAt';
    isVerifiedField = 'isPhoneVerified';
    console.log("[verifyOtpAndRegister] Using phone OTP fields");
    if (user.isPhoneVerified) {
      console.log("[verifyOtpAndRegister] User is already phone verified, checking for password auth");
      const hasPasswordAuth = user.auth?.identities?.some((identity) => identity.providerName === 'email');
      console.log("[verifyOtpAndRegister] hasPasswordAuth:", hasPasswordAuth);
      if (hasPasswordAuth) {
        console.log("[verifyOtpAndRegister] Phone already verified and has password, throwing error");
        throw new HttpError(409, "This phone number is already verified and has a password. Please log in.");
      }
    }
  }

  console.log(`[verifyOtpAndRegister] Checking OTP: user[${otpField}]=${user[otpField]}, args.otp=${args.otp}`);
  if (!user[otpField] || user[otpField] !== args.otp) {
    console.log("[verifyOtpAndRegister] Invalid OTP");
    throw new HttpError(400, "Invalid OTP.");
  }

  console.log(`[verifyOtpAndRegister] Checking OTP expiry: user[${otpExpiresAtField}]=${user[otpExpiresAtField]}, now=${new Date()}`);
  if (!user[otpExpiresAtField] || new Date() > new Date(user[otpExpiresAtField]!)) {
    console.log("[verifyOtpAndRegister] OTP has expired");
    throw new HttpError(400, "OTP has expired. Please request a new one.");
  }

  // Determine the email for AuthIdentity
  // Priority: 1. args.email (if provided), 2. args.identifier (if it's an email), 3. user.email (if exists), 4. placeholder for phone
  let authIdentityEmailToUse: string;
  if (args.email) {
    authIdentityEmailToUse = args.email;
    console.log("[verifyOtpAndRegister] Using args.email for authIdentityEmailToUse:", authIdentityEmailToUse);
  } else if (isEmailIdentifier) {
    authIdentityEmailToUse = args.identifier;
    console.log("[verifyOtpAndRegister] Using identifier (email) for authIdentityEmailToUse:", authIdentityEmailToUse);
  } else if (user.email) {
    authIdentityEmailToUse = user.email;
    console.log("[verifyOtpAndRegister] Using user.email for authIdentityEmailToUse:", authIdentityEmailToUse);
  } else { // Phone identifier and no args.email and no existing user.email
    authIdentityEmailToUse = `${args.identifier}@phone-auth.local`;
    console.log("[verifyOtpAndRegister] Using placeholder for phone for authIdentityEmailToUse:", authIdentityEmailToUse);
  }
  
  console.log("[verifyOtpAndRegister] Calling sanitizeAndSerializeProviderData");
  const providerData = await sanitizeAndSerializeProviderData({ hashedPassword: args.password });
  console.log("[verifyOtpAndRegister] providerData:", providerData);

  const hasExistingIdentityForAuthEmail = user.auth?.identities?.some(
    (identity) => identity.providerName === 'email' && identity.providerUserId === authIdentityEmailToUse
  );
  console.log("[verifyOtpAndRegister] hasExistingIdentityForAuthEmail:", hasExistingIdentityForAuthEmail, "user.isEmailVerified:", user?.isEmailVerified);

  if (hasExistingIdentityForAuthEmail && user?.isEmailVerified) {
    console.log("[verifyOtpAndRegister] Auth identity for this email already exists and user is verified, throwing error");
    throw new HttpError(409, `An account identity for ${authIdentityEmailToUse} already exists. Consider logging in or using a different email for this account.`);
  }

  try {
    // Use a transaction to ensure both user update and auth identity creation succeed together
    const updatedUser = await prisma.$transaction(async (tx) => {
      // First, update the user's verification status
      const userUpdateData: any = {
        [isVerifiedField]: true,
        [otpField]: null,
        [otpExpiresAtField]: null,
        ...(args.firstName && { firstName: args.firstName }),
        ...(args.lastName && { lastName: args.lastName }),
        // Update primary email if args.email is provided and different from current, 
        // or if identifier is an email and it's different from current user.email
        ...(args.email && args.email !== user.email && { email: args.email }), 
        ...(!args.email && isEmailIdentifier && args.identifier !== user.email && { email: args.identifier }),
      };

      const updatedUser = await tx.user.update({
        where: { id: user.id },
        data: userUpdateData,
        include: { auth: { include: { identities: true } } }
      });

      // Then, ensure Auth record exists
      const auth = await tx.auth.upsert({
        where: { userId: user.id },
        update: {},
        create: { userId: user.id },
      });

      // Check if AuthIdentity already exists for this email
      const existingAuthIdentity = await tx.authIdentity.findUnique({
        where: {
          providerName_providerUserId: {
            providerName: 'email',
            providerUserId: authIdentityEmailToUse,
          }
        }
      });

      // CRITICAL FIX: Create/Update AuthIdentity with proper email verification status
      // This is what Wasp does and what our login function checks
      if (!existingAuthIdentity) {
        // Create new AuthIdentity with email verification status
        let finalProviderData = providerData;
        if (isEmailIdentifier) {
          // Parse the provider data, set isEmailVerified: true, and re-serialize
          const parsedProviderData = JSON.parse(providerData);
          parsedProviderData.isEmailVerified = true;
          finalProviderData = JSON.stringify(parsedProviderData);
        }
        
        await tx.authIdentity.create({
          data: {
            authId: auth.id,
            providerName: 'email',
            providerUserId: authIdentityEmailToUse,
            providerData: finalProviderData,
          }
        });
        console.log("[verifyOtpAndRegister] Created new AuthIdentity for:", authIdentityEmailToUse, "with isEmailVerified:", isEmailIdentifier);
      } else {
        // Update existing AuthIdentity with new password and email verification status
        let finalProviderData = providerData;
        if (isEmailIdentifier) {
          // Parse the provider data, set isEmailVerified: true, and re-serialize
          const parsedProviderData = JSON.parse(providerData);
          parsedProviderData.isEmailVerified = true;
          finalProviderData = JSON.stringify(parsedProviderData);
        }
        
        await tx.authIdentity.update({
          where: {
            providerName_providerUserId: {
              providerName: 'email',
              providerUserId: authIdentityEmailToUse,
            }
          },
          data: {
            providerData: finalProviderData,
          }
        });
        console.log("[verifyOtpAndRegister] Updated existing AuthIdentity for:", authIdentityEmailToUse, "with isEmailVerified:", isEmailIdentifier);
      }

      return updatedUser;
    });

    console.log("[verifyOtpAndRegister] User updated successfully:", updatedUser && { id: updatedUser.id, email: updatedUser.email, mobileNumber: updatedUser.mobileNumber });

    return updatedUser;

  } catch (error: any) {
    console.error("[verifyOtpAndRegister] Error in try/catch:", error);
    if (error instanceof HttpError) throw error;
    if (error.code === 'P2002') {
      const target = error.meta?.target as string[] | undefined;
      console.error("[verifyOtpAndRegister] Prisma unique constraint error, target:", target);
      if (target?.includes('email') || target?.includes('User_email_key')) {
        // This error implies that args.email (or args.identifier if it was an email)
        // is trying to be set as the User's primary email, but it's already taken by another user.
        throw new HttpError(409, "The email address specified is already in use by another account.");
      } else if (target?.includes('AuthIdentity_providerName_providerUserId_key')) {
        // This implies authIdentityEmailToUse is already an existing AuthIdentity for another user.
        throw new HttpError(409, "Auth identity conflict. This email might be linked to another account's login credentials.");
      }
      console.error("[verifyOtpAndRegister] Error in verifyOtpAndRegister:", error);
      throw new HttpError(409, "A data conflict occurred during registration.");
    }
    console.error("[verifyOtpAndRegister] Error in verifyOtpAndRegister:", error);
    throw new HttpError(500, error.message || 'An unexpected error occurred during verification and registration.');
  }
};

// --- Login with Phone or Email Action ---

const loginWithPhoneOrEmailInputSchema = z.object({
  identifier: z.string().min(1, "Identifier (email or phone) is required"),
  password: z.string().min(1, "Password is required"),
});

export type LoginWithPhoneOrEmailData = z.infer<typeof loginWithPhoneOrEmailInputSchema>;

// Define the include structure for reuse and type generation
const userIncludeWithAuthAndIdentities = {
  auth: { 
    include: { 
      identities: true 
    }
  }
} satisfies Prisma.UserInclude;

// Derive the precise type from Prisma based on the include
type UserWithAuthAndIdentities = Prisma.UserGetPayload<{
  include: typeof userIncludeWithAuthAndIdentities
}>;

// Updated action to return sessionId
export const loginWithPhoneOrEmail: LoginWithPhoneOrEmail<LoginWithPhoneOrEmailData, { sessionId: string }> = async (rawArgs, context) => {
  const args = ensureArgsSchemaOrThrowHttpError(loginWithPhoneOrEmailInputSchema, rawArgs);
  console.log(args)
  const isEmailLogin = args.identifier.includes('@');
  let user: UserWithAuthAndIdentities | null = null;

  if (isEmailLogin) {
    user = await context.entities.User.findUnique({
      where: { email: args.identifier },
      include: userIncludeWithAuthAndIdentities,
    });
  } else {
    user = await context.entities.User.findFirst({
      where: { mobileNumber: args.identifier },
      include: userIncludeWithAuthAndIdentities,
    });
  }

  if (!user) {
    throw new HttpError(401, "Invalid credentials. User not found.");
  }

  if (!user.auth || !user.auth.identities || user.auth.identities.length === 0) {
    throw new HttpError(401, "Invalid credentials. Auth identity not found for user.");
  }

  const emailIdentity = user.auth.identities.find(
    (identity) => identity.providerName === 'email'
  );

  if (!emailIdentity) {
    throw new HttpError(401, "Invalid credentials. Email provider identity not setup correctly.");
  }

  let providerData;
  try {
    providerData = JSON.parse(emailIdentity.providerData);
  } catch (e) {
    console.error("Failed to parse AuthIdentity providerData:", e, "\nProvider Data:", emailIdentity.providerData);
    throw new HttpError(500, "Account configuration error parsing provider data.");
  }
  

  if (!providerData.hashedPassword) {
    throw new HttpError(500, "Password data is missing or corrupted for this user.");
  }

  // Perform verification checks
  if (isEmailLogin) {
    // For email login, check if the email associated with the AuthIdentity is verified
    if (providerData.isEmailVerified === false) { // Explicitly check for false
      throw new HttpError(403, "Email address not verified. Please check your email to verify your account.");
    }
  } else {
    // For phone login, check if the user's phone is verified
    if (!user.isPhoneVerified) {
      throw new HttpError(403, "Phone number is not verified. Please verify your phone number first.");
    }
  }

    try {
        await verifyPassword(providerData.hashedPassword, args.password);
    } catch(e) {
        throw createInvalidCredentialsError()
    }

  if (!user.auth.id) {
      throw new HttpError(500, "User authentication record is missing.");
  }

  // Credentials are valid, create a session
  const session = await createSession(user.auth.id);
  console.log("login with phone or email session", session)
  return { sessionId: session.id };
};

// --- Request Email OTP Action ---

const requestEmailOtpInputSchema = z.object({
  email: z.string().email("Invalid email format"),
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
  password: z.string().min(8, "Password must be at least 8 characters long"),
});

export type RequestEmailOtpData = z.infer<typeof requestEmailOtpInputSchema>;

export const requestEmailOtp: RequestPhoneOtp<RequestEmailOtpData, { message: string }> = async ( // Using RequestPhoneOtp type for consistency in return, might need a more generic type later
  rawArgs,
  context
) => {
  const args = ensureArgsSchemaOrThrowHttpError(requestEmailOtpInputSchema, rawArgs);

  if (!context.entities.User) {
    throw new HttpError(500, 'User entity not configured for this action.');
  }

  const COOLDOWN_PERIOD_MS = 60 * 1000; // 60 seconds

  // 1. Check for existing user and cooldown
  let existingUser = await context.entities.User.findUnique({
    where: { email: args.email },
  });

  if (existingUser && existingUser.lastEmailOtpSentAt) { // Assuming lastEmailOtpSentAt field
    const timeSinceLastOtp = Date.now() - new Date(existingUser.lastEmailOtpSentAt).getTime();
    if (timeSinceLastOtp < COOLDOWN_PERIOD_MS) {
      throw new HttpError(429, "Please wait before requesting another OTP for this email.");
    }
  }

  // 2. If user exists and is already email verified, they should log in (or reset password)
  // This part depends on how you want to handle it. For now, let's assume they can still request OTP for actions like "verify device" or similar.
  // Or, if OTP is purely for signup/initial verification:
  // if (existingUser && existingUser.isEmailVerified) { // Assuming isEmailVerified field
  //   throw new HttpError(409, "This email is already verified. Please log in.");
  // }

  // 3. Generate OTP and expiry
  const otp = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // OTP expires in 10 minutes
  const now = new Date();

  let userForEmail: Pick<User, 'email' | 'firstName'>;

  // const providerId = createProviderId("email", args.email);
  // const authIdentity = await findAuthIdentity(providerId);
  // 4. Create or Update user
  if (existingUser) {
    const providerId = createProviderId("email", args.email);
    const authIdentity = await findAuthIdentity(providerId);
    if(!authIdentity) {
      const providerData = await sanitizeAndSerializeProviderData({ hashedPassword: args.password });
      const updatedUser = await context.entities.User.update({
      where: { id: existingUser.id },
      data: {
        emailOtp: otp, // REMINDER: Add to User entity in schema.prisma
        emailOtpExpiresAt: otpExpiresAt, // REMINDER: Add to User entity in schema.prisma
        lastEmailOtpSentAt: now, // REMINDER: Add to User entity in schema.prisma
        ...(args.firstName && !existingUser.firstName && { firstName: args.firstName }),
        ...(args.lastName && !existingUser.lastName && { lastName: args.lastName }),
        auth: {
          create: {
            identities: {
              create: {
                providerName: 'email',
                providerUserId: args.email,
                providerData: providerData,
              }
            }
          }
        }
      },
    });
    userForEmail = { email: updatedUser.email!, firstName: updatedUser.firstName };
    } else {
      const updatedUser = await context.entities.User.update({
      where: { id: existingUser.id },
      data: {
        emailOtp: otp, // REMINDER: Add to User entity in schema.prisma
        emailOtpExpiresAt: otpExpiresAt, // REMINDER: Add to User entity in schema.prisma
        lastEmailOtpSentAt: now, // REMINDER: Add to User entity in schema.prisma
        ...(args.firstName && !existingUser.firstName && { firstName: args.firstName }),
        ...(args.lastName && !existingUser.lastName && { lastName: args.lastName }),
      },
     });
      userForEmail = { email: updatedUser.email!, firstName: updatedUser.firstName };
    }
    
   
  } else {
    const providerId = createProviderId('email', args.email);
    const providerData = await sanitizeAndSerializeProviderData({ hashedPassword: args.password });
    const additionalUserData = {
      email: args.email,
      firstName: args.firstName,
      lastName: args.lastName,
      role: Role.CUSTOMER, // Default role
      emailOtp: otp, // REMINDER: Add to User entity in schema.prisma
      emailOtpExpiresAt: otpExpiresAt, // REMINDER: Add to User entity in schema.prisma
      isEmailVerified: false, // Initially not verified
      lastEmailOtpSentAt: now, // REMINDER: Add to User entity in schema.prisma
    };
    const newUser = await createUser(
        providerId,
        providerData,
        {
            ...additionalUserData,
            email: args.email
        }
    );
    userForEmail = { email: newUser.email!, firstName: newUser.firstName };
  }
  
  if (!userForEmail.email) {
      throw new HttpError(500, "User email is unexpectedly missing after create/update.");
  }

  // 5. Send OTP Email
  try {
    await sendEmailVerificationEmail(
        userForEmail.email, // First argument: email address
        { // Second argument: options object
            to: userForEmail.email,
            subject: "Your Dalti Email OTP",
            text: `Your One-Time Password (OTP) for Dalti is: ${otp}\nThis OTP is valid for 10 minutes.`,
            html:
              `<p>Hi ${userForEmail.firstName || 'there'},</p>
               <p>Your One-Time Password (OTP) for Dalti is: <strong>${otp}</strong></p>
               <p>This OTP is valid for 10 minutes.</p>
               <p>If you did not request this, please ignore this email.</p>
               <p>Thanks,<br/>The Dalti Team</p>`,
        }
    );
    console.log(`Email OTP sent to ${userForEmail.email}`);
  } catch (emailError: any) {
    console.error(`Failed to send OTP email to ${userForEmail.email}:`, emailError);
    throw new HttpError(500, "Failed to send OTP email. Please try again.");
  }

  return { message: "OTP sent successfully to your email address. Please verify to complete the action." };
};

// --- Request Provider Phone OTP Action ---

const requestProviderPhoneOtpInputSchema = z.object({
  phoneNumber: z.string().min(1, "Phone number is required"),
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
});

export type RequestProviderPhoneOtpData = z.infer<typeof requestProviderPhoneOtpInputSchema>;

export const requestProviderPhoneOtp: RequestPhoneOtp<RequestProviderPhoneOtpData, { message: string; otp?: string }> = async (
  rawArgs,
  context
) => {
  const args = ensureArgsSchemaOrThrowHttpError(requestProviderPhoneOtpInputSchema, rawArgs);

  if (!context.entities.User) {
    throw new HttpError(500, 'User entity not configured for this action.');
  }

  const COOLDOWN_PERIOD_MS = 60 * 1000; // 60 seconds

  // 1. Check for existing user and cooldown
  let existingUser = await context.entities.User.findUnique({
    where: { mobileNumber: args.phoneNumber },
  });

  if (existingUser && existingUser.lastOtpSentAt) {
    const timeSinceLastOtp = Date.now() - new Date(existingUser.lastOtpSentAt).getTime();
    if (timeSinceLastOtp < COOLDOWN_PERIOD_MS) {
      throw new HttpError(429, "Please wait before requesting another OTP.");
    }
  }

  // 2. If user exists and is already verified, they should log in
  if (existingUser && existingUser.isPhoneVerified) {
    throw new HttpError(409, "This phone number is already verified. Please log in.");
  }

  // 3. Generate OTP and expiry
  const otp = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // OTP expires in 10 minutes
  const now = new Date();

  let userForSms: Pick<User, 'mobileNumber' | 'firstName'>;

  // 4. Create or Update user
  if (existingUser) {
    // User exists but is not verified, update OTP
    const updatedUser = await context.entities.User.update({
      where: { id: existingUser.id },
      data: {
        phoneOtp: otp,
        phoneOtpExpiresAt: otpExpiresAt,
        lastOtpSentAt: now,
        // Optionally update firstName/lastName if provided and different/missing
        ...(args.firstName && { firstName: args.firstName }),
        ...(args.lastName && { lastName: args.lastName }),
      },
    });
    userForSms = { mobileNumber: updatedUser.mobileNumber!, firstName: updatedUser.firstName };
  } else {
    // User does not exist, create new user with CLIENT role for providers
    const newUser = await context.entities.User.create({
      data: {
        mobileNumber: args.phoneNumber,
        firstName: args.firstName,
        lastName: args.lastName,
        role: Role.CLIENT, // CLIENT role for provider users
        phoneOtp: otp,
        phoneOtpExpiresAt: otpExpiresAt,
        isPhoneVerified: false,
        lastOtpSentAt: now,
      },
    });
    userForSms = { mobileNumber: newUser.mobileNumber!, firstName: newUser.firstName };
  }

  if (!userForSms.mobileNumber) {
      throw new HttpError(500, "User phone number is unexpectedly missing after create/update.");
  }

  // 5. Send OTP SMS
  const smsSentSuccessfully = await sendOtpSms(userForSms.mobileNumber, otp);

  if (!smsSentSuccessfully) {
    // Potentially roll back user creation/update or log for manual review
    // For now, we'll throw an error, which means the user might exist with an OTP that wasn't sent.
    throw new HttpError(500, "Failed to send OTP. Please try again.");
  }

  // 6. Return response with optional OTP for test mode
  const response: { message: string; otp?: string } = {
    message: "OTP sent successfully to your phone number. Please verify to complete registration."
  };

  // Include OTP in response if test mode is enabled
  if (isOtpTestModeEnabled()) {
    response.otp = otp;
  }

  return response;
};

// --- Request Provider Email OTP Action ---

const requestProviderEmailOtpInputSchema = z.object({
  email: z.string().email("Invalid email format"),
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
  password: z.string().min(8, "Password must be at least 8 characters long"),
});

export type RequestProviderEmailOtpData = z.infer<typeof requestProviderEmailOtpInputSchema>;

export const requestProviderEmailOtp: RequestPhoneOtp<RequestProviderEmailOtpData, { message: string; otp?: string }> = async (
  rawArgs,
  context
) => {
  const args = ensureArgsSchemaOrThrowHttpError(requestProviderEmailOtpInputSchema, rawArgs);

  if (!context.entities.User) {
    throw new HttpError(500, 'User entity not configured for this action.');
  }

  const COOLDOWN_PERIOD_MS = 60 * 1000; // 60 seconds

  // 1. Check for existing user and cooldown
  let existingUser = await context.entities.User.findUnique({
    where: { email: args.email },
  });

  if (existingUser && existingUser.lastEmailOtpSentAt) {
    const timeSinceLastOtp = Date.now() - new Date(existingUser.lastEmailOtpSentAt).getTime();
    if (timeSinceLastOtp < COOLDOWN_PERIOD_MS) {
      throw new HttpError(429, "Please wait before requesting another OTP for this email.");
    }
  }

  // 2. Generate OTP and expiry
  const otp = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // OTP expires in 10 minutes
  const now = new Date();

  let userForEmail: Pick<User, 'email' | 'firstName'>;

  // 3. Create or Update user
  if (existingUser) {
    const updatedUser = await context.entities.User.update({
      where: { id: existingUser.id },
      data: {
        emailOtp: otp,
        emailOtpExpiresAt: otpExpiresAt,
        lastEmailOtpSentAt: now,
        ...(args.firstName && !existingUser.firstName && { firstName: args.firstName }),
        ...(args.lastName && !existingUser.lastName && { lastName: args.lastName }),
      },
    });
    userForEmail = { email: updatedUser.email!, firstName: updatedUser.firstName };
  } else {
    const providerId = createProviderId('email', args.email);
    const providerData = await sanitizeAndSerializeProviderData({ hashedPassword: args.password });
    const additionalUserData = {
      email: args.email,
      firstName: args.firstName,
      lastName: args.lastName,
      role: Role.CLIENT, // CLIENT role for provider users
      emailOtp: otp,
      emailOtpExpiresAt: otpExpiresAt,
      isEmailVerified: false,
      lastEmailOtpSentAt: now,
    };
    const newUser = await createUser(
        providerId,
        providerData,
        {
            ...additionalUserData,
            email: args.email
        }
    );
    userForEmail = { email: newUser.email!, firstName: newUser.firstName };
  }

  if (!userForEmail.email) {
      throw new HttpError(500, "User email is unexpectedly missing after create/update.");
  }

  // 4. Send OTP Email
  try {
    await sendEmailVerificationEmail(
        userForEmail.email,
        {
            to: userForEmail.email,
            subject: "Your Provider Registration OTP",
            text: `Your One-Time Password (OTP) for provider registration is: ${otp}\nThis OTP is valid for 10 minutes.`,
            html:
              `<p>Hi ${userForEmail.firstName || 'there'},</p>
               <p>Your One-Time Password (OTP) for provider registration is: <strong>${otp}</strong></p>
               <p>This OTP is valid for 10 minutes.</p>
               <p>If you did not request this, please ignore this email.</p>
               <p>Thanks,<br/>The Provider Registration Team</p>`,
        }
    );
    console.log(`Provider email OTP sent to ${userForEmail.email}`);
  } catch (emailError: any) {
    console.error(`Failed to send provider OTP email to ${userForEmail.email}:`, emailError);
    throw new HttpError(500, "Failed to send OTP email. Please try again.");
  }

  // 5. Return response with optional OTP for test mode
  const response: { message: string; otp?: string } = {
    message: "OTP sent successfully to your email address. Please verify to complete provider registration."
  };

  // Include OTP in response if test mode is enabled
  if (isOtpTestModeEnabled()) {
    response.otp = otp;
  }

  return response;
};

// --- Password Reset Actions ---

// Request Password Reset OTP Action
const requestPasswordResetOtpInputSchema = z.object({
  email: z.string().email("Invalid email format"),
});

export type RequestPasswordResetOtpData = z.infer<typeof requestPasswordResetOtpInputSchema>;

export const requestPasswordResetOtp: RequestPhoneOtp<RequestPasswordResetOtpData, { message: string }> = async (
  rawArgs,
  context
) => {
  const args = ensureArgsSchemaOrThrowHttpError(requestPasswordResetOtpInputSchema, rawArgs);

  if (!context.entities.User) {
    throw new HttpError(500, 'User entity not configured for this action.');
  }

  const COOLDOWN_PERIOD_MS = 60 * 1000; // 60 seconds

  // 1. Check if user exists
  const existingUser = await context.entities.User.findUnique({
    where: { email: args.email },
    include: { auth: { include: { identities: true } } }
  });

  if (!existingUser) {
    throw new HttpError(404, "No account found with this email address.");
  }

  // 2. Check if user has email auth identity (password login)
  const hasEmailAuth = existingUser.auth?.identities?.some(
    (identity) => identity.providerName === 'email'
  );

  if (!hasEmailAuth) {
    throw new HttpError(400, "This account does not have password login enabled. Please contact support.");
  }

  // 3. Check cooldown period
  if (existingUser.lastEmailOtpSentAt) {
    const timeSinceLastOtp = Date.now() - new Date(existingUser.lastEmailOtpSentAt).getTime();
    if (timeSinceLastOtp < COOLDOWN_PERIOD_MS) {
      throw new HttpError(429, "Please wait before requesting another password reset OTP.");
    }
  }

  // 4. Generate OTP and expiry
  const otp = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // OTP expires in 10 minutes
  const now = new Date();

  // 5. Update user with reset OTP
  await context.entities.User.update({
    where: { id: existingUser.id },
    data: {
      emailOtp: otp,
      emailOtpExpiresAt: otpExpiresAt,
      lastEmailOtpSentAt: now,
    },
  });

  // 6. Send OTP Email
  try {
    await sendEmailVerificationEmail(
      existingUser.email!,
      {
        to: existingUser.email!,
        subject: "Password Reset OTP - Dalti",
        text: `Your password reset OTP is: ${otp}\nThis OTP is valid for 10 minutes.`,
        html: `
          <p>Hi ${existingUser.firstName || 'there'},</p>
          <p>You requested to reset your password for your Dalti account.</p>
          <p>Your One-Time Password (OTP) is: <strong>${otp}</strong></p>
          <p>This OTP is valid for 10 minutes.</p>
          <p>If you did not request this password reset, please ignore this email.</p>
          <p>Thanks,<br/>The Dalti Team</p>
        `,
      }
    );
    console.log(`Password reset OTP sent to ${existingUser.email}`);
  } catch (emailError: any) {
    console.error(`Failed to send password reset OTP email to ${existingUser.email}:`, emailError);
    throw new HttpError(500, "Failed to send password reset OTP email. Please try again.");
  }

  return { message: "Password reset OTP sent successfully to your email address." };
};

// Verify Password Reset OTP Action
const verifyPasswordResetOtpInputSchema = z.object({
  email: z.string().email("Invalid email format"),
  otp: z.string().length(6, "OTP must be 6 digits"),
});

export type VerifyPasswordResetOtpData = z.infer<typeof verifyPasswordResetOtpInputSchema>;

export const verifyPasswordResetOtp: RequestPhoneOtp<VerifyPasswordResetOtpData, { resetToken: string; message: string }> = async (
  rawArgs,
  context
) => {
  const args = ensureArgsSchemaOrThrowHttpError(verifyPasswordResetOtpInputSchema, rawArgs);

  if (!context.entities.User) {
    throw new HttpError(500, 'User entity not configured for this action.');
  }

  // 1. Find user by email
  const user = await context.entities.User.findUnique({
    where: { email: args.email },
  });

  if (!user) {
    throw new HttpError(404, "No account found with this email address.");
  }

  // 2. Verify OTP
  if (!user.emailOtp || user.emailOtp !== args.otp) {
    throw new HttpError(400, "Invalid OTP.");
  }

  // 3. Check OTP expiry
  if (!user.emailOtpExpiresAt || new Date() > new Date(user.emailOtpExpiresAt)) {
    throw new HttpError(400, "OTP has expired. Please request a new password reset.");
  }

  // 4. Generate reset token (valid for 30 minutes)
  const resetToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  const resetTokenExpiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes

  // 5. Update user with reset token and clear OTP
  await context.entities.User.update({
    where: { id: user.id },
    data: {
      emailOtp: null,
      emailOtpExpiresAt: null,
      passwordResetToken: resetToken,
      passwordResetTokenExpiresAt: resetTokenExpiresAt,
    },
  });

  return {
    resetToken,
    message: "OTP verified successfully. Use the reset token to set your new password."
  };
};

// Reset Password Action
const resetPasswordInputSchema = z.object({
  resetToken: z.string().min(1, "Reset token is required"),
  newPassword: z.string().min(8, "Password must be at least 8 characters long"),
});

export type ResetPasswordData = z.infer<typeof resetPasswordInputSchema>;

export const resetPassword: RequestPhoneOtp<ResetPasswordData, { message: string }> = async (
  rawArgs,
  context
) => {
  const args = ensureArgsSchemaOrThrowHttpError(resetPasswordInputSchema, rawArgs);

  if (!context.entities.User) {
    throw new HttpError(500, 'User entity not configured for this action.');
  }

  // 1. Find user by reset token
  const user = await context.entities.User.findFirst({
    where: { passwordResetToken: args.resetToken },
    include: { auth: { include: { identities: true } } }
  });

  if (!user) {
    throw new HttpError(400, "Invalid or expired reset token.");
  }

  // 2. Check token expiry
  if (!user.passwordResetTokenExpiresAt || new Date() > new Date(user.passwordResetTokenExpiresAt)) {
    throw new HttpError(400, "Reset token has expired. Please request a new password reset.");
  }

  // 3. Find email auth identity
  const emailIdentity = user.auth?.identities?.find(
    (identity) => identity.providerName === 'email'
  );

  if (!emailIdentity) {
    throw new HttpError(500, "Email authentication not found for this user.");
  }

  // 4. Update password in auth identity
  try {
    const newProviderData = await sanitizeAndSerializeProviderData({ hashedPassword: args.newPassword });

    // Parse existing provider data to preserve other fields
    let existingProviderData;
    try {
      existingProviderData = JSON.parse(emailIdentity.providerData);
    } catch (e) {
      existingProviderData = {};
    }

    // Merge new password with existing data
    const updatedProviderData = JSON.stringify({
      ...existingProviderData,
      ...JSON.parse(newProviderData)
    });

    await prisma.$transaction(async (tx) => {
      // Update auth identity with new password
      await tx.authIdentity.update({
        where: {
          providerName_providerUserId: {
            providerName: 'email',
            providerUserId: emailIdentity.providerUserId,
          }
        },
        data: {
          providerData: updatedProviderData,
        }
      });

      // Clear reset token from user
      await tx.user.update({
        where: { id: user.id },
        data: {
          passwordResetToken: null,
          passwordResetTokenExpiresAt: null,
        },
      });
    });

    return { message: "Password reset successfully. You can now log in with your new password." };

  } catch (error: any) {
    console.error("Error resetting password:", error);
    if (error instanceof HttpError) throw error;
    throw new HttpError(500, "Failed to reset password. Please try again.");
  }
};
