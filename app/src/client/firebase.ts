// src/client/firebase.ts
import { initializeApp, getApp, getApps } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
// import { getAnalytics } from "firebase/analytics"; // Optional, if you use Analytics

// Your web app's Firebase configuration
// IMPORTANT: Consider using environment variables for these, especially if your repo is public.
// Wasp has .env files for server, for client-side config, you might hardcode it here or
// use a build-time environment variable solution if <PERSON><PERSON> supports it easily for client.
// For FCM, this config is generally considered safe to be in client code as it's needed to identify your app.
const firebaseConfig = {
  apiKey: "AIzaSyD3z86nxd9i4okjur85vpg4be-um8lQzn8", //  REPLACE WITH YOUR ACTUAL KEY
  authDomain: "dalti-3d06b.firebaseapp.com",      //  REPLACE WITH YOURS
  projectId: "dalti-3d06b",                       //  REPLACE WITH YOURS
  storageBucket: "dalti-3d06b.firebasestorage.app", //  REPLACE WITH YOURS
  messagingSenderId: "816987655237",              //  REPLACE WITH YOURS
  appId: "1:816987655237:web:99b535475db49b632e672c", //  REPLACE WITH YOURS
  measurementId: "G-0BKG02FNLZ"                     //  REPLACE WITH YOURS (Optional)
};

// Initialize Firebase
let app;
if (!getApps().length) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApp(); // If already initialized, get the existing instance
}

// const analytics = getAnalytics(app); // Optional
const messaging = getMessaging(app);

export { app, messaging, getToken, onMessage };