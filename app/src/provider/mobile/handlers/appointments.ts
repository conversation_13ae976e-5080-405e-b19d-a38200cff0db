/**
 * Provider Appointment API Handlers
 * Handles provider appointment management endpoints
 */

import type { Request, Response } from 'express';
import {
  sendSuccess,
  sendError,
  sendCreated,
  asyncHandler,
  type ProviderApiContext
} from '../index';
import {
  createAppointmentSchema,
  updateAppointmentSchema,
  appointmentFiltersSchema,
  validateAndExtract,
  transformCreateAppointmentData
} from '../utils/validationUtils';
import {
  getAppointments as getAppointmentsOp,
  createAppointment as createAppointmentOp,
  updateAppointment as updateAppointmentOp,
  completeAppointment as completeAppointmentOp,
  noShowAppointment as noShowAppointmentOp
} from '../../operations';
import type { AppointmentResponse, CreateAppointmentRequest, UpdateAppointmentRequest } from '../types';
import dayjs from 'dayjs'; // Import dayjs
import utc from 'dayjs/plugin/utc.js';
import timezone from 'dayjs/plugin/timezone.js';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter.js'; // Import the missing plugin
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore.js'; // Import the missing plugin
import { getTranslatedString, translateAndStore } from '../../../server/translations';
import { LanguageCode } from '@prisma/client';
import { prisma } from 'wasp/server';

/**
 * Helper function to translate appointment fields and related entities
 */
const translateAppointmentFields = async (
  appointment: any,
  targetLanguage: LanguageCode,
  context: any
) => {
  const promises: Promise<string | null>[] = [];

  // Translate Appointment fields
  promises.push(
    getTranslatedString(prisma, 'Appointment', String(appointment.id), 'notes', targetLanguage)
  );

  // Translate Service fields if service exists
  if ((appointment as any).service) {
    promises.push(
      getTranslatedString(prisma, 'Service', String((appointment as any).service.id), 'title', targetLanguage),
      getTranslatedString(prisma, 'Service', String((appointment as any).service.id), 'description', targetLanguage)
    );
  } else {
    promises.push(Promise.resolve(null), Promise.resolve(null));
  }

  // Translate SProvidingPlace (location) fields if place exists
  if ((appointment as any).place) {
    promises.push(
      getTranslatedString(prisma, 'SProvidingPlace', String((appointment as any).place.id), 'name', targetLanguage),
      getTranslatedString(prisma, 'SProvidingPlace', String((appointment as any).place.id), 'shortName', targetLanguage),
      getTranslatedString(prisma, 'SProvidingPlace', String((appointment as any).place.id), 'address', targetLanguage),
      getTranslatedString(prisma, 'SProvidingPlace', String((appointment as any).place.id), 'city', targetLanguage),
      getTranslatedString(prisma, 'SProvidingPlace', String((appointment as any).place.id), 'country', targetLanguage)
    );
  } else {
    promises.push(Promise.resolve(null), Promise.resolve(null), Promise.resolve(null), Promise.resolve(null), Promise.resolve(null));
  }

  // Translate Queue fields if queue exists
  if ((appointment as any).queue) {
    promises.push(
      getTranslatedString(prisma, 'Queue', String((appointment as any).queue.id), 'title', targetLanguage)
    );
  } else {
    promises.push(Promise.resolve(null));
  }

  // Translate Customer name fields if customer exists
  if ((appointment as any).customerFolder?.customer) {
    promises.push(
      getTranslatedString(prisma, 'Customer', String((appointment as any).customerFolder.customer.id), 'firstName', targetLanguage),
      getTranslatedString(prisma, 'Customer', String((appointment as any).customerFolder.customer.id), 'lastName', targetLanguage)
    );
  } else {
    promises.push(Promise.resolve(null), Promise.resolve(null));
  }

  const [
    translatedNotes,
    translatedServiceTitle,
    translatedServiceDescription,
    translatedPlaceName,
    translatedPlaceShortName,
    translatedPlaceAddress,
    translatedPlaceCity,
    translatedPlaceCountry,
    translatedQueueTitle,
    translatedCustomerFirstName,
    translatedCustomerLastName
  ] = await Promise.all(promises);

  return {
    notes: translatedNotes || appointment.notes,
    service: (appointment as any).service ? {
      title: translatedServiceTitle || (appointment as any).service.title,
      description: translatedServiceDescription || (appointment as any).service.description
    } : null,
    place: (appointment as any).place ? {
      name: translatedPlaceName || (appointment as any).place.name,
      shortName: translatedPlaceShortName || (appointment as any).place.shortName,
      address: translatedPlaceAddress || (appointment as any).place.address,
      city: translatedPlaceCity || (appointment as any).place.city,
      country: translatedPlaceCountry || (appointment as any).place.country
    } : null,
    queue: (appointment as any).queue ? {
      title: translatedQueueTitle || (appointment as any).queue.title
    } : null,
    customer: (appointment as any).customerFolder?.customer ? {
      firstName: translatedCustomerFirstName || (appointment as any).customerFolder.customer.firstName,
      lastName: translatedCustomerLastName || (appointment as any).customerFolder.customer.lastName
    } : null
  };
};

/**
 * Helper function to save translations for appointment fields
 */
const saveAppointmentTranslations = async (appointment: any, context: any) => {
  try {
    // Save translations for Appointment fields
    if (appointment.notes) {
      await translateAndStore(
        prisma,
        String(appointment.id),
        'Appointment',
        'notes',
        appointment.notes
      );
    }

    console.log(`[saveAppointmentTranslations] Successfully saved translations for appointment ID: ${appointment.id}`);
  } catch (error) {
    console.error(`[saveAppointmentTranslations] Error saving translations for appointment ID: ${appointment.id}`, error);
    // Don't throw error to avoid breaking the main operation
  }
};

/**
 * GET /api/providers/appointments
 * Get all provider appointments with filtering and pagination
 */
export const getProviderAppointments = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate query parameters
    const queryData = validateAndExtract(appointmentFiltersSchema, req.query);

    // Call the operation to get appointments
    const appointments = await getAppointmentsOp(undefined, context);
    console.log(`[getProviderAppointments] Appointments: ${appointments.length}`);

    // Apply filters if provided
    let filteredAppointments = appointments;
    
    if (queryData.status) {
      filteredAppointments = filteredAppointments.filter(apt => apt.status === queryData.status);
    }
    console.log(`[getProviderAppointments] filteredAppointments 1: ${filteredAppointments.length}`);
    if (queryData.startDate) {
      // Remove the space between date and time part
      const startDateStr = queryData.startDate.replace(' ', '');
      const startDate = new Date(startDateStr);
      console.log(`[getProviderAppointments] Start date: ${startDate}`);
      filteredAppointments = filteredAppointments.filter(apt =>
        apt.expectedAppointmentStartTime && apt.expectedAppointmentStartTime >= startDate
      );
    }

    console.log(`[getProviderAppointments] filteredAppointments 2: ${filteredAppointments.length}`);

    if (queryData.endDate) {
      const endDateStr = queryData.endDate.replace(' ', '');
      const endDate = new Date(endDateStr);
      console.log(`[getProviderAppointments] End date: ${endDate}`);
      filteredAppointments = filteredAppointments.filter(apt =>
        apt.expectedAppointmentStartTime && apt.expectedAppointmentStartTime <= endDate
      );
    }

    console.log(`[getProviderAppointments] filteredAppointments 3: ${filteredAppointments.length}`);

    if (queryData.serviceId) {
      filteredAppointments = filteredAppointments.filter(apt => apt.serviceId === queryData.serviceId);
    }

    if (queryData.queueId) {
      filteredAppointments = filteredAppointments.filter(apt => apt.queueId === queryData.queueId);
    }

    // Apply pagination
    const page = queryData.page || 1;
    const limit = queryData.limit || 20;
    const skip = (page - 1) * limit;
    const paginatedAppointments = filteredAppointments.slice(skip, skip + limit);

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations in parallel for all appointments
    const translationPromises = paginatedAppointments.filter(appointment => appointment.status !== 'canceled').map(appointment =>
      translateAppointmentFields(appointment, userPreferredLanguage, context)
    );
    const translatedFields = await Promise.all(translationPromises);

    // Transform to response format with translated values
    const response: AppointmentResponse[] = paginatedAppointments.filter(appointment => appointment.status !== 'canceled').map((appointment, index) => {
      const translated = translatedFields[index];

      return {
        id: appointment.id,
        status: appointment.status,
        expectedAppointmentStartTime: appointment.expectedAppointmentStartTime || undefined,
        expectedAppointmentEndTime: appointment.expectedAppointmentEndTime || undefined,
        realAppointmentStartTime: appointment.realAppointmentStartTime || undefined,
        realAppointmentEndTime: appointment.realAppointmentEndTime || undefined,
        notes: translated.notes || undefined,
        service: {
          id: (appointment as any).service.id,
          title: translated.service?.title || (appointment as any).service.title,
          duration: (appointment as any).service.duration,
          price: 0, // Default price since field doesn't exist in DB yet
          pointsRequirements: (appointment as any).service.pointsRequirements || 0,
          isPublic: true, // Default value since field doesn't exist in DB yet
          deliveryType: 'at_location', // Default value since field doesn't exist in DB yet
          servedRegions: [], // Default empty array since field doesn't exist in DB yet
          description: translated.service?.description || undefined,
          color: (appointment as any).service.color || '#000000',
          acceptOnline: (appointment as any).service.acceptOnline || false,
          acceptNew: (appointment as any).service.acceptNew || false,
          notificationOn: (appointment as any).service.notificationOn || false
        },
        place: {
          id: (appointment as any).place.id,
          name: translated.place?.name || (appointment as any).place.name,
          isMobileHidden: false,
          parking: false,
          elevator: false,
          handicapAccess: false,
          queues: (appointment as any).place.queues || []
        },
        queue: (appointment as any).queue ? {
          id: (appointment as any).queue.id,
          title: translated.queue?.title || (appointment as any).queue.title,
          isActive: true,
          sProvidingPlaceId: (appointment as any).place.id,
        services: []
      } : undefined,
        customer: {
          id: (appointment as any).customerFolder.userId,
          firstName: translated.customer?.firstName || (appointment as any).customerFolder.customer?.firstName,
          lastName: translated.customer?.lastName || (appointment as any).customerFolder.customer?.lastName
        }
      };
    });

    // Send response with pagination info
    sendSuccess(res, {
      appointments: response,
      pagination: {
        page,
        limit,
        total: filteredAppointments.length,
        totalPages: Math.ceil(filteredAppointments.length / limit),
        hasNext: skip + limit < filteredAppointments.length,
        hasPrev: page > 1
      }
    }, 'Provider appointments retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderAppointments] Error:', error);
    sendError(res, error, 'Failed to retrieve provider appointments');
  }
});

/**
 * GET /api/providers/appointments/:id
 * Get a single provider appointment by ID
 */
export const getProviderAppointment = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get appointment ID from URL parameters
    const appointmentId = parseInt(req.params.id);
    if (!appointmentId || isNaN(appointmentId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid appointment ID' });
    }

    // Get the user's provider to ensure they can only access their own appointments
    const userProvider = await context.entities.SProvider.findUnique({
      where: { userId: context.user.id },
      select: { id: true }
    });

    if (!userProvider) {
      return sendError(res, { statusCode: 403, message: 'Provider profile not found' });
    }

    // Fetch the appointment with all necessary includes
    const appointment = await context.entities.Appointment.findFirst({
      where: {
        id: appointmentId,
        service: {
          sProviderId: userProvider.id
        }
      },
      include: {
        service: {
          include: {
            category: true
          }
        },
        place: {
          include: {
            queues: true
          }
        },
        queue: true,
        customerFolder: {
          include: {
            customer: true
          }
        }
      }
    });

    if (!appointment) {
      return sendError(res, { statusCode: 404, message: 'Appointment not found' });
    }

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the appointment and related entities
    const translated = await translateAppointmentFields(appointment, userPreferredLanguage, context);

    // Transform to response format with translated values
    const response: AppointmentResponse = {
      id: appointment.id,
      status: appointment.status,
      expectedAppointmentStartTime: appointment.expectedAppointmentStartTime || undefined,
      expectedAppointmentEndTime: appointment.expectedAppointmentEndTime || undefined,
      realAppointmentStartTime: appointment.realAppointmentStartTime || undefined,
      realAppointmentEndTime: appointment.realAppointmentEndTime || undefined,
      notes: translated.notes || undefined,
      service: {
        id: appointment.service.id,
        title: translated.service?.title || appointment.service.title,
        duration: appointment.service.duration,
        price: 0, // Default price since field doesn't exist in DB yet
        pointsRequirements: appointment.service.pointsRequirements || 0,
        isPublic: true, // Default value since field doesn't exist in DB yet
        deliveryType: 'at_location', // Default value since field doesn't exist in DB yet
        servedRegions: [], // Default empty array since field doesn't exist in DB yet
        description: translated.service?.description || undefined,
        color: appointment.service.color || '#000000',
        acceptOnline: appointment.service.acceptOnline || false,
        acceptNew: appointment.service.acceptNew || false,
        notificationOn: appointment.service.notificationOn || false
      },
      place: {
        id: appointment.place.id,
        name: translated.place?.name || appointment.place.name,
        isMobileHidden: false,
        parking: false,
        elevator: false,
        handicapAccess: false,
        queues: appointment.place.queues || []
      },
      queue: appointment.queue ? {
        id: appointment.queue.id,
        title: translated.queue?.title || appointment.queue.title,
        isActive: true,
        sProvidingPlaceId: appointment.place.id,
        services: []
      } : undefined,
      customer: {
        id: appointment.customerFolder.userId,
        firstName: translated.customer?.firstName || appointment.customerFolder.customer?.firstName,
        lastName: translated.customer?.lastName || appointment.customerFolder.customer?.lastName
      }
    };

    sendSuccess(res, response, 'Provider appointment retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderAppointment] Error:', error);
    sendError(res, error, 'Failed to retrieve provider appointment');
  }
});

/**
 * POST /api/providers/appointments
 * Create a new provider appointment
 */
export const createProviderAppointment = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }
    console.log("createProviderAppointment", req.body)
    
    // Validate request data using the mobile API schema
    const mobileData = validateAndExtract(createAppointmentSchema, req.body);

    // Get the service details to determine duration and placeId
    const service = await context.entities.Service.findUnique({
      where: { id: mobileData.serviceId },
      select: { id: true, duration: true }
    });

    if (!service) {
      return sendError(res, { statusCode: 404, message: 'Service not found' });
    }

    // Get the queue to determine the placeId
    const queue = await context.entities.Queue.findUnique({
      where: { id: mobileData.queueId },
      select: { id: true, sProvidingPlaceId: true }
    });

    if (!queue) {
      return sendError(res, { statusCode: 404, message: 'Queue not found' });
    }

    // Transform mobile API format to backend operation format
    const appointmentData = transformCreateAppointmentData(
      mobileData,
      service.duration,
      queue.sProvidingPlaceId
    );

    // Call the operation to create the appointment
    const newAppointment = await createAppointmentOp(appointmentData, context);

    // Save translations for the newly created appointment
    await saveAppointmentTranslations(newAppointment, context);

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the created appointment
    const translated = await translateAppointmentFields(newAppointment, userPreferredLanguage, context);

    // Transform to response format
    const response: AppointmentResponse = {
      id: newAppointment.id,
      status: newAppointment.status,
      expectedAppointmentStartTime: newAppointment.expectedAppointmentStartTime || undefined,
      expectedAppointmentEndTime: newAppointment.expectedAppointmentEndTime || undefined,
      realAppointmentStartTime: newAppointment.realAppointmentStartTime || undefined,
      realAppointmentEndTime: newAppointment.realAppointmentEndTime || undefined,
      notes: translated.notes || undefined,
      service: {
        id: (newAppointment as any).service.id,
        title: translated.service?.title || (newAppointment as any).service.title,
        duration: (newAppointment as any).service.duration,
        price: 0, // Default price since field doesn't exist in DB yet
        pointsRequirements: (newAppointment as any).service.pointsRequirements || 0,
        isPublic: true, // Default value since field doesn't exist in DB yet
        deliveryType: 'at_location', // Default value since field doesn't exist in DB yet
        servedRegions: [], // Default empty array since field doesn't exist in DB yet
        description: translated.service?.description || undefined,
        color: (newAppointment as any).service.color || '#000000',
        acceptOnline: (newAppointment as any).service.acceptOnline || false,
        acceptNew: (newAppointment as any).service.acceptNew || false,
        notificationOn: (newAppointment as any).service.notificationOn || false
      },
      place: {
        id: (newAppointment as any).place.id,
        name: translated.place?.name || (newAppointment as any).place.name,
        isMobileHidden: false,
        parking: false,
        elevator: false,
        handicapAccess: false,
        queues: (newAppointment as any).place.queues || []
      },
      queue: (newAppointment as any).queue ? {
        id: (newAppointment as any).queue.id,
        title: translated.queue?.title || (newAppointment as any).queue.title,
        isActive: true,
        sProvidingPlaceId: (newAppointment as any).place.id,
        services: []
      } : undefined,
      customer: {
        id: (newAppointment as any).customerFolder.userId,
        firstName: translated.customer?.firstName || (newAppointment as any).customerFolder.customer?.firstName,
        lastName: translated.customer?.lastName || (newAppointment as any).customerFolder.customer?.lastName
      }
    };

    sendCreated(res, response, 'Provider appointment created successfully');
  } catch (error: any) {
    console.error('[createProviderAppointment] Error:', error);
    sendError(res, error, 'Failed to create provider appointment');
  }
});

/**
 * PATCH /api/providers/appointments/:id
 * Update a provider appointment
 */
export const updateProviderAppointment = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get appointment ID from URL parameters
    const appointmentId = parseInt(req.params.id);
    if (!appointmentId || isNaN(appointmentId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid appointment ID' });
    }

    const appointment = await context.entities.Appointment.findUnique({
      where: { id: appointmentId },
      include: {
        service: true,
        queue: true,
        customerFolder: true,
        place: true
      }
    });

    if (!appointment) {
      return sendError(res, { statusCode: 404, message: 'Appointment not found' });
    }

    // Validate request data
    const updateData = validateAndExtract(updateAppointmentSchema, req.body);

    console.log("updateData", updateData)

    const updatedAppointment = await updateAppointmentOp({
      appointmentId: appointmentId,
      customerUserId: appointment?.customerFolder?.userId,
      serviceId: updateData.serviceId || appointment?.service?.id,
      placeId: appointment?.place?.id,
      queueId: updateData.queueId || appointment?.queue?.id,
      startTime: updateData.expectedStartTime || appointment.expectedAppointmentStartTime,
      endTime: updateData.expectedEndTime || appointment.expectedAppointmentEndTime,
      status: appointment.status,
      notes: updateData.notes || appointment.notes
    }, context);

    // Save translations for the updated appointment
    await saveAppointmentTranslations(updatedAppointment, context);

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the updated appointment
    const translated = await translateAppointmentFields(updatedAppointment, userPreferredLanguage, context);

    console.log("updatedAppointment", updatedAppointment)

    // Return translated appointment response
    const response: AppointmentResponse = {
      id: updatedAppointment.id,
      status: updatedAppointment.status,
      expectedAppointmentStartTime: updatedAppointment.expectedAppointmentStartTime || undefined,
      expectedAppointmentEndTime: updatedAppointment.expectedAppointmentEndTime || undefined,
      realAppointmentStartTime: updatedAppointment.realAppointmentStartTime || undefined,
      realAppointmentEndTime: updatedAppointment.realAppointmentEndTime || undefined,
      notes: translated.notes || undefined,
      service: {
        id: (updatedAppointment as any).service?.id || appointment.service.id,
        title: translated.service?.title || (updatedAppointment as any).service?.title || appointment.service.title,
        duration: (updatedAppointment as any).service?.duration || appointment.service.duration,
        price: 0, // Default price since field doesn't exist in DB yet
        pointsRequirements: (updatedAppointment as any).service?.pointsRequirements || appointment.service.pointsRequirements || 0,
        isPublic: true, // Default value since field doesn't exist in DB yet
        deliveryType: 'at_location', // Default value since field doesn't exist in DB yet
        servedRegions: [], // Default empty array since field doesn't exist in DB yet
        description: translated.service?.description || undefined,
        color: (updatedAppointment as any).service?.color || appointment.service.color || '#000000',
        acceptOnline: (updatedAppointment as any).service?.acceptOnline || appointment.service.acceptOnline || false,
        acceptNew: (updatedAppointment as any).service?.acceptNew || appointment.service.acceptNew || false,
        notificationOn: (updatedAppointment as any).service?.notificationOn || appointment.service.notificationOn || false
      },
      place: {
        id: (updatedAppointment as any).place?.id || appointment.place.id,
        name: translated.place?.name || (updatedAppointment as any).place?.name || appointment.place.name,
        isMobileHidden: false,
        parking: false,
        elevator: false,
        handicapAccess: false,
        queues: (updatedAppointment as any).place?.queues || appointment.place.queues || []
      },
      queue: (updatedAppointment as any).queue || appointment.queue ? {
        id: ((updatedAppointment as any).queue?.id || appointment.queue?.id),
        title: translated.queue?.title || (updatedAppointment as any).queue?.title || appointment.queue?.title,
        isActive: true,
        sProvidingPlaceId: (updatedAppointment as any).place?.id || appointment.place.id,
        services: []
      } : undefined,
      customer: {
        id: (updatedAppointment as any).customerFolder?.userId || appointment.customerFolder.userId,
        firstName: translated.customer?.firstName || (updatedAppointment as any).customerFolder?.customer?.firstName || appointment.customerFolder.customer?.firstName,
        lastName: translated.customer?.lastName || (updatedAppointment as any).customerFolder?.customer?.lastName || appointment.customerFolder.customer?.lastName
      }
    };

    sendSuccess(res, response, 'Provider appointment updated successfully');
  } catch (error: any) {
    console.error('[updateProviderAppointment] Error:', error);
    sendError(res, error, 'Failed to update provider appointment');
  }
});

/**
 * PATCH /api/providers/appointments/:id/status
 * Update appointment status (complete, no-show, etc.)
 */
export const updateAppointmentStatus = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get appointment ID from URL parameters
    const appointmentId = parseInt(req.params.id);
    if (!appointmentId || isNaN(appointmentId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid appointment ID' });
    }

    // Validate status
    const { status, notes } = req.body;
    if (!status) {
      return sendError(res, { statusCode: 400, message: 'Status is required' });
    }

    const validStatuses = ['pending', 'confirmed', 'InProgress', 'completed', 'canceled', 'noshow'];
    if (!validStatuses.includes(status)) {
      return sendError(res, { statusCode: 400, message: 'Invalid status value' });
    }



    const appointment = await context.entities.Appointment.findUnique({
      where: { id: appointmentId },
      include: {
        service: true,
        queue: true,
        customerFolder: true,
        place: true
      }
    });

    const startTime = dayjs(appointment.expectedAppointmentStartTime);

    const updatedAppointment = await updateAppointmentOp({
      appointmentId: appointmentId,
      customerUserId: appointment?.customerFolder?.userId,
      serviceId: appointment?.service?.id,
      placeId: appointment?.place?.id,
      queueId: appointment?.queue?.id,
      startTime: appointment?.expectedAppointmentStartTime,
      endTime: appointment?.expectedAppointmentEndTime,
      status: status,
      notes: notes || null
    }, context);
    
    // For now, return a simple success response since the operations
    // require complex data that would need to be fetched first
    // const updatedAppointment = {
    //   id: appointmentId,
    //   status: status,
    //   notes: notes || null,
    // };

    // Transform to response format (simplified for now)
    const response = {
      id: updatedAppointment.id,
      status: updatedAppointment.status,
      notes: updatedAppointment.notes,
      message: `Appointment status updated to ${status}`
    };

    sendSuccess(res, response, 'Appointment status updated successfully');
  } catch (error: any) {
    console.error('[updateAppointmentStatus] Error:', error);
    sendError(res, error, 'Failed to update appointment status');
  }
});
