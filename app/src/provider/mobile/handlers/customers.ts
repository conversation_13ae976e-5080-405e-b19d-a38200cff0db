/**
 * Provider Customer API Handlers
 * Handles provider customer management endpoints
 */

import type { Request, Response } from 'express';
import {
  sendSuccess,
  sendError,
  sendCreated,
  asyncHandler,
  type ProviderApiContext
} from '../index';
import {
  createCustomerSchema,
  updateCustomerSchema,
  validateAndExtract
} from '../utils/validationUtils';
import {
  getProviderCustomers as getProviderCustomersOp,
  createProviderCustomer as createProviderCustomerOp,
  updateProviderCustomer as updateProviderCustomerOp
} from '../../operations';
import type { CustomerResponse, CreateCustomerRequest, UpdateCustomerRequest } from '../types';
import { getTranslatedString, translateAndStore } from '../../../server/translations';
import { LanguageCode } from '@prisma/client';
import { prisma } from 'wasp/server';

/**
 * Helper function to translate customer folder fields and customer name fields
 */
const translateCustomerFolderFields = async (
  customerFolder: any,
  targetLanguage: LanguageCode,
  context: any
) => {
  const promises: Promise<string | null>[] = [];

  // Translate CustomerFolder fields
  promises.push(
    getTranslatedString(prisma, 'CustomerFolder', String(customerFolder.id), 'notes', targetLanguage)
  );

  // Translate Customer name fields if customer exists
  if (customerFolder.customer) {
    promises.push(
      getTranslatedString(prisma, 'Customer', String(customerFolder.customer.id), 'firstName', targetLanguage),
      getTranslatedString(prisma, 'Customer', String(customerFolder.customer.id), 'lastName', targetLanguage)
    );
  } else {
    promises.push(Promise.resolve(null), Promise.resolve(null));
  }

  const [translatedNotes, translatedFirstName, translatedLastName] = await Promise.all(promises);

  return {
    notes: translatedNotes || customerFolder.notes,
    customer: customerFolder.customer ? {
      firstName: translatedFirstName || customerFolder.customer.firstName,
      lastName: translatedLastName || customerFolder.customer.lastName
    } : null
  };
};

/**
 * Helper function to save translations for customer folder fields and customer name fields
 */
const saveCustomerFolderTranslations = async (customerFolder: any, context: any) => {
  try {
    // Save translations for CustomerFolder fields
    if (customerFolder.notes) {
      await translateAndStore(
        prisma,
        String(customerFolder.id),
        'CustomerFolder',
        'notes',
        customerFolder.notes
      );
    }

    // Save translations for Customer name fields if customer exists
    if (customerFolder.customer) {
      if (customerFolder.customer.firstName) {
        await translateAndStore(
          prisma,
          String(customerFolder.customer.id),
          'Customer',
          'firstName',
          customerFolder.customer.firstName
        );
      }

      if (customerFolder.customer.lastName) {
        await translateAndStore(
          prisma,
          String(customerFolder.customer.id),
          'Customer',
          'lastName',
          customerFolder.customer.lastName
        );
      }
    }

    console.log(`[saveCustomerFolderTranslations] Successfully saved translations for customer folder ID: ${customerFolder.id}`);
  } catch (error) {
    console.error(`[saveCustomerFolderTranslations] Error saving translations for customer folder ID: ${customerFolder.id}`, error);
    // Don't throw error to avoid breaking the main operation
  }
};

/**
 * GET /api/providers/customers
 * Get all provider customers with search and pagination
 */
export const getProviderCustomers = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Extract query parameters
    const { search, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Call the operation to get customers
    const customerFolders = await getProviderCustomersOp(undefined, context);

    // Apply translations in parallel for all customer folders
    const translationPromises = customerFolders.map(folder =>
      translateCustomerFolderFields(folder, userPreferredLanguage, context)
    );
    const translatedFields = await Promise.all(translationPromises);

    // Transform to response format with translated values
    const customers: CustomerResponse[] = customerFolders.map((folder: any, index) => {
      const translated = translatedFields[index];

      return {
        id: folder.customer.id,
        firstName: translated.customer?.firstName || folder.customer.firstName,
        lastName: translated.customer?.lastName || folder.customer.lastName,
        mobileNumber: folder.customer.mobileNumber,
        email: folder.customer.email,
        nationalId: folder.customer.nationalId,
        notes: translated.notes,
        appointmentCount: folder.appointments?.length || 0,
        createdAt: folder.createdAt
      };
    });

    // Apply search filter if provided
    let filteredCustomers = customers;
    if (search && typeof search === 'string') {
      const searchLower = search.toLowerCase();
      filteredCustomers = customers.filter(customer => 
        customer.firstName.toLowerCase().includes(searchLower) ||
        customer.lastName.toLowerCase().includes(searchLower) ||
        customer.mobileNumber.includes(search) ||
        (customer.email && customer.email.toLowerCase().includes(searchLower))
      );
    }

    // Apply sorting
    filteredCustomers.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'name':
          aValue = `${a.firstName} ${a.lastName}`.toLowerCase();
          bValue = `${b.firstName} ${b.lastName}`.toLowerCase();
          break;
        case 'email':
          aValue = a.email?.toLowerCase() || '';
          bValue = b.email?.toLowerCase() || '';
          break;
        case 'createdAt':
        default:
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
      }
      
      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Apply pagination
    const pageNum = parseInt(page as string) || 1;
    const limitNum = parseInt(limit as string) || 20;
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedCustomers = filteredCustomers.slice(startIndex, endIndex);

    const response = {
      data: paginatedCustomers,
      pagination: {
        total: filteredCustomers.length,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(filteredCustomers.length / limitNum),
        hasNext: endIndex < filteredCustomers.length,
        hasPrev: pageNum > 1
      }
    };

    sendSuccess(res, response, 'Provider customers retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderCustomers] Error:', error);
    sendError(res, error, 'Failed to retrieve provider customers');
  }
});

/**
 * GET /api/providers/customers/:id
 * Get a single provider customer by ID
 */
export const getProviderCustomer = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get customer ID from URL parameters
    const customerId = req.params.id;
    if (!customerId) {
      return sendError(res, { statusCode: 400, message: 'Customer ID is required' });
    }

    // Get all customers and find the specific one
    const customerFolders = await getProviderCustomersOp(undefined, context);
    const customerFolder = customerFolders.find((folder: any) => folder.customer.id === customerId);

    if (!customerFolder) {
      return sendError(res, { statusCode: 404, message: 'Customer not found' });
    }

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the customer folder
    const translated = await translateCustomerFolderFields(customerFolder, userPreferredLanguage, context);

    // Transform to response format
    const customer: CustomerResponse = {
      id: customerFolder.customer.id,
      firstName: translated.customer?.firstName || customerFolder.customer.firstName,
      lastName: translated.customer?.lastName || customerFolder.customer.lastName,
      mobileNumber: customerFolder.customer.mobileNumber,
      email: customerFolder.customer.email,
      nationalId: customerFolder.customer.nationalId,
      notes: translated.notes,
      appointmentCount: customerFolder.appointments?.length || 0,
      createdAt: customerFolder.createdAt
    };

    sendSuccess(res, customer, 'Provider customer retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderCustomer] Error:', error);
    sendError(res, error, 'Failed to retrieve provider customer');
  }
});

/**
 * POST /api/providers/customers
 * Create a new provider customer
 */
export const createProviderCustomer = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const customerData = validateAndExtract(createCustomerSchema, req.body);
    
    // Call the operation to create the customer
    const newCustomerFolder = await createProviderCustomerOp(customerData, context);

    // Get the created customer details by fetching updated customer list
    const customerFolders = await getProviderCustomersOp(undefined, context);
    const createdCustomerFolder = customerFolders.find((folder: any) => folder.id === newCustomerFolder.id);

    if (!createdCustomerFolder) {
      return sendError(res, { statusCode: 500, message: 'Failed to retrieve created customer' });
    }

    // Save translations for the newly created customer folder (with complete data)
    await saveCustomerFolderTranslations(createdCustomerFolder, context);

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the created customer folder
    const translated = await translateCustomerFolderFields(createdCustomerFolder, userPreferredLanguage, context);

    // Transform to response format
    const response: CustomerResponse = {
      id: createdCustomerFolder.customer.id,
      firstName: translated.customer?.firstName || createdCustomerFolder.customer.firstName,
      lastName: translated.customer?.lastName || createdCustomerFolder.customer.lastName,
      mobileNumber: createdCustomerFolder.customer.mobileNumber,
      email: createdCustomerFolder.customer.email,
      nationalId: createdCustomerFolder.customer.nationalId,
      notes: translated.notes,
      appointmentCount: 0, // New customer has no appointments
      createdAt: createdCustomerFolder.createdAt
    };

    sendCreated(res, response, 'Provider customer created successfully');
  } catch (error: any) {
    console.error('[createProviderCustomer] Error:', error);
    sendError(res, error, 'Failed to create provider customer');
  }
});

/**
 * PUT /api/providers/customers/:id
 * Update a provider customer
 */
export const updateProviderCustomer = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get customer ID from URL parameters
    const customerId = req.params.id;
    if (!customerId) {
      return sendError(res, { statusCode: 400, message: 'Customer ID is required' });
    }

    // Validate request data
    const updateData = validateAndExtract(updateCustomerSchema, req.body);
    
    // Call the operation to update the customer
    const updatedCustomerFolder = await updateProviderCustomerOp({
      customerUserId: customerId,
      ...updateData
    }, context);

    // Save translations for the updated customer folder
    await saveCustomerFolderTranslations(updatedCustomerFolder, context);

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the updated customer folder
    const translated = await translateCustomerFolderFields(updatedCustomerFolder, userPreferredLanguage, context);

    // Transform to response format
    const response: CustomerResponse = {
      id: updatedCustomerFolder.customer.id,
      firstName: translated.customer?.firstName || updatedCustomerFolder.customer.firstName,
      lastName: translated.customer?.lastName || updatedCustomerFolder.customer.lastName,
      mobileNumber: updatedCustomerFolder.customer.mobileNumber,
      email: updatedCustomerFolder.customer.email,
      nationalId: updatedCustomerFolder.customer.nationalId,
      notes: translated.notes,
      appointmentCount: 0, // Would need separate query to get appointment count
      createdAt: updatedCustomerFolder.createdAt
    };

    sendSuccess(res, response, 'Provider customer updated successfully');
  } catch (error: any) {
    console.error('[updateProviderCustomer] Error:', error);
    sendError(res, error, 'Failed to update provider customer');
  }
});
