/**
 * Provider Service API Handlers
 * Handles provider service management endpoints
 */

import type { Request, Response } from 'express';
import {
  sendSuccess,
  sendError,
  sendCreated,
  asyncHandler,
  validateAndExtract,
  createServiceSchema,
  updateServiceSchema,
  type ProviderApiContext,
  type ServiceResponse,
  type CreateServiceRequest
} from '../index';
import { createService, getServices, updateService, deleteService } from '../../operations';
import { getTranslatedString, translateAndStore } from '../../../server/translations';
import { LanguageCode } from '@prisma/client';
import { prisma } from 'wasp/server';

/**
 * Helper function to translate service fields
 */
const translateServiceFields = async (
  service: any,
  targetLanguage: LanguageCode,
  context: any
) => {
  const promises: Promise<string | null>[] = [];

  // Translate Service fields
  promises.push(
    getTranslatedString(prisma, 'Service', String(service.id), 'title', targetLanguage),
    getTranslatedString(prisma, 'Service', String(service.id), 'description', targetLanguage)
  );

  const [translatedTitle, translatedDescription] = await Promise.all(promises);

  return {
    title: translatedTitle || service.title,
    description: translatedDescription || service.description
  };
};

/**
 * Helper function to save translations for service fields
 */
const saveServiceTranslations = async (service: any, context: any) => {
  try {
    // Save translations for Service fields
    if (service.title) {
      await translateAndStore(
        prisma,
        String(service.id),
        'Service',
        'title',
        service.title
      );
    }

    if (service.description) {
      await translateAndStore(
        prisma,
        String(service.id),
        'Service',
        'description',
        service.description
      );
    }

    console.log(`[saveServiceTranslations] Successfully saved translations for service ID: ${service.id}`);
  } catch (error) {
    console.error(`[saveServiceTranslations] Error saving translations for service ID: ${service.id}`, error);
    // Don't throw error to avoid breaking the main operation
  }
};

/**
 * GET /api/providers/services
 * Get all provider services
 */
export const getProviderServices = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Get provider services using existing operation
    const services = await getServices({}, context);

    // Apply translations in parallel for all services
    const translationPromises = services.map(service =>
      translateServiceFields(service, userPreferredLanguage, context)
    );
    const translatedFields = await Promise.all(translationPromises);

    // Format response with translated values
    const response: ServiceResponse[] = services.map((service, index) => {
      const translated = translatedFields[index];

      return {
        id: service.id,
        title: translated.title,
        duration: service.duration,
        price: service.price || 0, // Default value - field doesn't exist in database yet
        pointsRequirements: service.pointsRequirements,
        isPublic: service.isPublic || true, // Default value - field doesn't exist in database yet
        deliveryType: service.deliveryType || 'at_location', // Default value - field doesn't exist in database yet
        servedRegions: typeof service.servedRegions === 'string' ? JSON.parse(service.servedRegions) : (service.servedRegions || []),
        description: translated.description || undefined,
        color: service.color || '#000000',
        acceptOnline: service.acceptOnline,
        acceptNew: service.acceptNew,
        notificationOn: service.notificationOn
      };
    });

    sendSuccess(res, response, 'Provider services retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderServices] Error:', error);
    sendError(res, error, 'Failed to retrieve provider services');
  }
});
const tryJSONParse = (value: any) => {
  try {
    return JSON.parse(value);
  } catch (error) {
    return value;
  }
};
/**
 * GET /api/providers/services/:id
 * Get a specific provider service by ID
 */
export const getProviderService = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get service ID from URL parameters
    const serviceId = parseInt(req.params.id);
    if (!serviceId || isNaN(serviceId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid service ID' });
    }

    // Get all provider services and find the specific one
    const services = await getServices({}, context);
    const service = services.find(s => s.id === serviceId);

    if (!service) {
      return sendError(res, { statusCode: 404, message: 'Service not found' });
    }

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the service
    const translated = await translateServiceFields(service, userPreferredLanguage, context);

    // Format response according to API standards
    const response: ServiceResponse = {
      id: service.id,
      title: translated.title,
      duration: service.duration,
      price: service.price || 0,
      pointsRequirements: service.pointsRequirements,
      isPublic: service.isPublic || true,
      deliveryType: service.deliveryType || 'at_location',
      servedRegions: typeof service.servedRegions === 'string' ? tryJSONParse(service.servedRegions) : (service.servedRegions || []),
      description: translated.description || undefined,
      color: service.color || '#000000',
      acceptOnline: service.acceptOnline,
      acceptNew: service.acceptNew,
      notificationOn: service.notificationOn
    };

    sendSuccess(res, response, 'Provider service retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderService] Error:', error);
    sendError(res, error, 'Failed to retrieve provider service');
  }
});

/**
 * POST /api/providers/services
 * Create a new provider service
 */
export const createProviderService = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const serviceData = validateAndExtract(createServiceSchema, req.body);

    // Ensure color is provided or set default
    const serviceDataWithDefaults = {
      ...serviceData,
      color: serviceData.color || '#000000'
    };
    console.log('serviceDataWithDefaults', serviceDataWithDefaults);
    // Create service using existing operation
    const newService = await createService(serviceDataWithDefaults, context);

    // Save translations for the newly created service
    await saveServiceTranslations(newService, context);

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the created service
    const translated = await translateServiceFields(newService, userPreferredLanguage, context);

    // Format response according to API standards
    const response: ServiceResponse = {
      id: newService.id,
      title: translated.title,
      duration: newService.duration,
      price: 0, // Default value - field doesn't exist in database yet
      pointsRequirements: newService.pointsRequirements,
      isPublic: true, // Default value - field doesn't exist in database yet
      deliveryType: 'at_location', // Default value - field doesn't exist in database yet
      servedRegions: [], // Default value - field doesn't exist in database yet
      description: translated.description || undefined,
      color: newService.color || '#000000',
      acceptOnline: newService.acceptOnline,
      acceptNew: newService.acceptNew,
      notificationOn: newService.notificationOn
    };

    sendCreated(res, response, 'Provider service created successfully');
  } catch (error: any) {
    console.error('[createProviderService] Error:', error);
    sendError(res, error, 'Failed to create provider service');
  }
});

/**
 * PATCH /api/providers/services/:id
 * Update a provider service
 */
export const updateProviderService = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get service ID from URL parameters
    const serviceId = parseInt(req.params.id);
    if (!serviceId || isNaN(serviceId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid service ID' });
    }
    // console.log('req.body', req.body);
    // Validate request data
    const updateData = validateAndExtract(updateServiceSchema, req.body);
    console.log('updateData', updateData);
    // First, get the current service to merge with update data
    const currentServices = await getServices({}, context);
    const currentService = currentServices.find(s => s.id === serviceId);

    if (!currentService) {
      return sendError(res, { statusCode: 404, message: 'Service not found' });
    }

    // Handle servedRegions to ensure it's always a non-optional string array
    const servedRegions = updateData.servedRegions === null 
      ? [] 
      : updateData.servedRegions ?? ((currentService as any).servedRegions || []);

    // Prepare data for the operation with all required fields
    const operationData = {
      serviceId,
      title: updateData.title ?? currentService.title,
      duration: updateData.duration ?? currentService.duration,
      price: updateData.price ?? ((currentService as any).price || 0),
      pointsRequirements: updateData.pointsRequirements ?? currentService.pointsRequirements,
      isPublic: updateData.isPublic ?? ((currentService as any).isPublic || true),
      deliveryType: updateData.deliveryType ?? ((currentService as any).deliveryType || 'at_location'),
      servedRegions,
      description: updateData.description ?? ((currentService as any).description || undefined),
      color: updateData.color ?? (currentService.color || '#000000'),
      acceptOnline: updateData.acceptOnline ?? currentService.acceptOnline,
      acceptNew: updateData.acceptNew ?? currentService.acceptNew,
      notificationOn: updateData.notificationOn ?? currentService.notificationOn
    };

    // Update service using existing operation
    const updatedService: any = await updateService(operationData, context);

    // Save translations for the updated service
    await saveServiceTranslations(updatedService, context);

    // Get user's preferred language (fallback to EN)
    const userPreferredLanguage = context.user.preferedLanguage || LanguageCode.EN;

    // Apply translations to the updated service
    const translated = await translateServiceFields(updatedService, userPreferredLanguage, context);

    // Format response according to API standards
    const response: ServiceResponse = {
      id: updatedService.id,
      title: translated.title,
      duration: updatedService.duration,
      price: 0, // Default value - field doesn't exist in database yet
      pointsRequirements: updatedService.pointsRequirements,
      isPublic: true, // Default value - field doesn't exist in database yet
      deliveryType: updatedService.deliveryType || 'at_location', // Default value - field doesn't exist in database yet
      servedRegions: typeof updatedService.servedRegions === 'string' ? JSON.parse(updatedService.servedRegions) : (updatedService.servedRegions || []),
      description: translated.description || undefined,
      color: updatedService.color || '#000000',
      acceptOnline: updatedService.acceptOnline,
      acceptNew: updatedService.acceptNew,
      notificationOn: updatedService.notificationOn
    };

    sendSuccess(res, response, 'Provider service updated successfully');
  } catch (error: any) {
    console.error('[updateProviderService] Error:', error);
    sendError(res, error, 'Failed to update provider service');
  }
});

/**
 * DELETE /api/providers/services/:id
 * Delete a provider service
 */
export const deleteProviderService = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Get service ID from URL parameters
    const serviceId = parseInt(req.params.id);
    if (!serviceId || isNaN(serviceId)) {
      return sendError(res, { statusCode: 400, message: 'Invalid service ID' });
    }

    // Delete service using existing operation
    const deletedService = await deleteService({ serviceId }, context);

    // Format response according to API standards
    const response: ServiceResponse = {
      id: deletedService.id,
      title: deletedService.title,
      duration: deletedService.duration,
      price: 0, // Default value - field doesn't exist in database yet
      pointsRequirements: deletedService.pointsRequirements,
      isPublic: true, // Default value - field doesn't exist in database yet
      deliveryType: 'at_location', // Default value - field doesn't exist in database yet
      servedRegions: [], // Default value - field doesn't exist in database yet
      description: undefined, // Default value - field doesn't exist in database yet
      color: deletedService.color || '#000000',
      acceptOnline: deletedService.acceptOnline,
      acceptNew: deletedService.acceptNew,
      notificationOn: deletedService.notificationOn
    };

    sendSuccess(res, response, 'Provider service deleted successfully');
  } catch (error: any) {
    console.error('[deleteProviderService] Error:', error);
    sendError(res, error, 'Failed to delete provider service');
  }
});
