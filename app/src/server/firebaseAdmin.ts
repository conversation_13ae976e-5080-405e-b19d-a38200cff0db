    // src/server/firebaseAdmin.ts
    import admin from 'firebase-admin';
    console.log('Initializing Firebase Admin SDK' , admin);
    if (!admin?.apps?.length) {
      try {
        const serviceAccountConfigString = process.env.FIREBASE_ADMIN_SDK_CONFIG;
        // console.log('serviceAccountConfigString', serviceAccountConfigString);
        if (!serviceAccountConfigString) {
          throw new Error('FIREBASE_ADMIN_SDK_CONFIG environment variable is not set.');
        }
        const serviceAccount:any = ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          // databaseURL: `https://${serviceAccount.project_id}.firebaseio.com` // Optional: if using Realtime Database
        });
        console.log('Firebase Admin SDK initialized successfully.');
      } catch (error: any) {
        console.error('Firebase Admin SDK initialization error:', error.message);
        // You might want to throw the error or handle it more gracefully depending on your app's needs
      }
    }

    export default admin;