apiVersion: v1
kind: Service
metadata:
  name: dalti-saas-postgres # This service name is used in the DATABASE_URL (e.g., dalti-saas-postgres.yachfin-medical-system-tests.svc.cluster.local)
  namespace: yachfin-medical-system-tests
  labels:
    app: dalti-saas-postgres
spec:
  selector:
    app: dalti-saas-postgres # Selects the pods managed by the StatefulSet
  ports:
    - port: 5432
      targetPort: 5432
      protocol: TCP
      name: postgresql
  # type: ClusterIP # Recommended type for internal access only
  type: LoadBalancer # As per your example, exposes the DB externally. Review security!
    # externalIPs:
    #   - *********** # Using the example IP. Replace with your desired static external IP if needed, or remove for dynamic assignment (provider dependent). 