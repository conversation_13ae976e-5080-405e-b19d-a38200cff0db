apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: dalti-saas-api-hpa
  namespace: yachfin-medical-system-tests
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: dalti-saas-api
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        # Scale up when average CPU utilization across pods exceeds 80%
        averageUtilization: 80
  # You can also add memory-based scaling:
  # - type: Resource
  #   resource:
  #     name: memory
  #     target:
  #       type: Utilization
  #       averageUtilization: 80
  # Behavior configuration (optional, for fine-tuning scaling speed)
  # behavior:
  #   scaleDown:
  #     stabilizationWindowSeconds: 300
  #     policies:
  #     - type: Percent
  #       value: 100
  #       periodSeconds: 15
  #   scaleUp:
  #     stabilizationWindowSeconds: 0
  #     policies:
  #     - type: Percent
  #       value: 100
  #       periodSeconds: 15
  #     - type: Pods
  #       value: 4
  #       periodSeconds: 15
  #     selectPolicy: Max 