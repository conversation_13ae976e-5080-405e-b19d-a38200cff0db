apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dalti-saas-api-ingress
  namespace: yachfin-medical-system-tests
  annotations:
    # Assuming you are using Nginx Ingress Controller
    nginx.ingress.kubernetes.io/rewrite-target: / # Rewrites path to / for the backend service
    # Add other annotations as needed, e.g., for SSL:
    # cert-manager.io/cluster-issuer: letsencrypt-prod # Example for cert-manager
    # nginx.ingress.kubernetes.io/ssl-redirect: "true"
    # Add proxy protocol annotation if needed (like your example)
    # nginx.ingress.kubernetes.io/use-proxy-protocol: "true"
spec:
  ingressClassName: nginx # As per your example
  # tls: # Example TLS configuration (requires cert-manager or manual secret)
  # - hosts:
  #   - dalti.adscloud.org
  #   - dapi.adscloud.org
  #   secretName: dalti-saas-tls-secret # Name of the TLS secret
  rules:
  - host: dapi.adscloud.org # Hostname for API access
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: dalti-saas-api-service # Points to the renamed API service
            port:
              number: 80 # Points to the Service port 