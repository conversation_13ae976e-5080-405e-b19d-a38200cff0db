apiVersion: apps/v1
kind: Deployment
metadata:
  name: dalti-saas-api
  namespace: yachfin-medical-system-tests
  labels:
    app: dalti-saas-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dalti-saas-api
  template:
    metadata:
      labels:
        app: dalti-saas-api
    spec:
      nodeSelector:
        node-type: worker
      imagePullSecrets:
        - name: my-dockerhub-secret
      containers:
        - name: dalti-saas-api
          image: kotoubm7/dapi-saas:Beta-1.0.1
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              name: http
          env:
            # Server configuration
            - name: PORT
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: PORT
            # - name: HOST
            #   valueFrom:
            #     configMapKeyRef:
            #       name: dalti-saas-config
            #       key: HOST
            - name: WASP_ALLOWED_CORS_ORIGINS
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: WASP_ALLOWED_CORS_ORIGINS

            # Wasp URLs
            - name: WASP_WEB_CLIENT_URL
              value: "https://dalti.adscloud.org"
            - name: WASP_API_URL
              value: "https://dapi.adscloud.org"
            - name: REACT_APP_API_URL
              value: "https://dapi.adscloud.org"
            - name: WASP_SERVER_URL
              value: "https://dapi.adscloud.org"

            # Database
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: DATABASE_URL

            # JWT Secret
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: JWT_SECRET

            # Stripe configuration
            - name: STRIPE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: STRIPE_API_KEY
            - name: STRIPE_WEBHOOK_SECRET
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: STRIPE_WEBHOOK_SECRET
            - name: STRIPE_CUSTOMER_PORTAL_URL
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: STRIPE_CUSTOMER_PORTAL_URL

            # LemonSqueezy configuration
            - name: LEMONSQUEEZY_API_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: LEMONSQUEEZY_API_KEY
            - name: LEMONSQUEEZY_WEBHOOK_SECRET
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: LEMONSQUEEZY_WEBHOOK_SECRET
            - name: LEMONSQUEEZY_STORE_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: LEMONSQUEEZY_STORE_ID

            # Payment plan IDs
            - name: PAYMENTS_HOBBY_SUBSCRIPTION_PLAN_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: PAYMENTS_HOBBY_SUBSCRIPTION_PLAN_ID
            - name: PAYMENTS_PRO_SUBSCRIPTION_PLAN_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: PAYMENTS_PRO_SUBSCRIPTION_PLAN_ID
            - name: PAYMENTS_CREDITS_10_PLAN_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: PAYMENTS_CREDITS_10_PLAN_ID
            - name: PAYMENTS_FREE_PLAN_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: PAYMENTS_FREE_PLAN_ID

            # Chargily configuration
            - name: CHARGILY_API_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: CHARGILY_API_KEY
            - name: CHARGILY_MODE
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: CHARGILY_MODE
            - name: CHARGILY_WEBHOOK_SECRET
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: CHARGILY_WEBHOOK_SECRET
            - name: CHARGILY_HOBBY_PLAN_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: CHARGILY_HOBBY_PLAN_ID
            - name: CHARGILY_PRO_PLAN_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: CHARGILY_PRO_PLAN_ID
            - name: CHARGILY_CREDITS_10_PLAN_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: CHARGILY_CREDITS_10_PLAN_ID
            - name: CHARGILY_FREE_PLAN_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: CHARGILY_FREE_PLAN_ID

            # Admin configuration
            - name: ADMIN_EMAILS
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: ADMIN_EMAILS

            # Google OAuth
            - name: GOOGLE_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: GOOGLE_CLIENT_ID
            - name: GOOGLE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: GOOGLE_CLIENT_SECRET

            # SendGrid
            - name: SENDGRID_API_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: SENDGRID_API_KEY

            # OpenAI
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: OPENAI_API_KEY

            # Plausible Analytics
            - name: PLAUSIBLE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: PLAUSIBLE_API_KEY
            - name: PLAUSIBLE_SITE_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: PLAUSIBLE_SITE_ID
            - name: PLAUSIBLE_BASE_URL
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: PLAUSIBLE_BASE_URL

            # Google Analytics
            - name: GOOGLE_ANALYTICS_CLIENT_EMAIL
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: GOOGLE_ANALYTICS_CLIENT_EMAIL
            - name: GOOGLE_ANALYTICS_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: GOOGLE_ANALYTICS_PRIVATE_KEY
            - name: GOOGLE_ANALYTICS_PROPERTY_ID
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: GOOGLE_ANALYTICS_PROPERTY_ID

            # AWS S3
            - name: AWS_S3_IAM_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: AWS_S3_IAM_ACCESS_KEY
            - name: AWS_S3_IAM_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: AWS_S3_IAM_SECRET_KEY
            - name: AWS_S3_FILES_BUCKET
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: AWS_S3_FILES_BUCKET
            - name: AWS_S3_REGION
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: AWS_S3_REGION

            # Email configuration
            - name: MAILGUN_API_KEY
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: MAILGUN_API_KEY
            - name: MAILGUN_DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: MAILGUN_DOMAIN
            - name: MAILGUN_API_URL
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: MAILGUN_API_URL
            - name: SMTP_HOST
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: SMTP_HOST
            - name: SMTP_USERNAME
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: SMTP_USERNAME
            - name: SMTP_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: SMTP_PASSWORD
            - name: SMTP_PORT
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: SMTP_PORT
            - name: SKIP_EMAIL_VERIFICATION_IN_DEV
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: SKIP_EMAIL_VERIFICATION_IN_DEV

            # Firebase Admin SDK
            - name: FIREBASE_ADMIN_SDK_CONFIG
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: FIREBASE_ADMIN_SDK_CONFIG

            # DeepL
            - name: DEEPL_API_KEY_1
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: DEEPL_API_KEY_1
            - name: DEEPL_API_KEY_2
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: DEEPL_API_KEY_2

            # Test configuration
            - name: TEST_API_URL
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: TEST_API_URL
            - name: TEST_USER_EMAIL
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: TEST_USER_EMAIL
            - name: TEST_USER_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: dalti-saas-secrets
                  key: TEST_USER_PASSWORD
            - name: OTP_TEST_MODE
              valueFrom:
                configMapKeyRef:
                  name: dalti-saas-config
                  key: OTP_TEST_MODE

          resources:
            requests:
              memory: "450Mi"
              cpu: "250m"
            limits:
              memory: "600Mi"
              cpu: "500m"

          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 15
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3