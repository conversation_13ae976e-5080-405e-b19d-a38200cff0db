apiVersion: v1
kind: ConfigMap
metadata:
  name: dalti-saas-config
  namespace: yachfin-medical-system-tests
data:
  # Database configuration
  POSTGRES_DB: "daltidb"
  POSTGRES_USER: "daltiuser"

  # Server configuration
  PORT: "8080"
  # HOST: "************"
  WASP_ALLOWED_CORS_ORIGINS: "*"

  # Stripe configuration
  STRIPE_CUSTOMER_PORTAL_URL: "https://billing.stripe.com/p/login/test_28obKR5mm2INa8U000"

  # LemonSqueezy configuration
  LEMONSQUEEZY_STORE_ID: "173729"

  # Payment plan IDs
  PAYMENTS_HOBBY_SUBSCRIPTION_PLAN_ID: "775659"
  PAYMENTS_PRO_SUBSCRIPTION_PLAN_ID: "775654"
  PAYMENTS_CREDITS_10_PLAN_ID: "775666"
  PAYMENTS_FREE_PLAN_ID: "778100"

  # Chargily configuration
  CHARGILY_MODE: "test"
  CHARGILY_HOBBY_PLAN_ID: "01k15zm95sew67yff2zmmwgktn"
  CHARGILY_PRO_PLAN_ID: "01k15zm968ry2k98pagsdfj40j"
  CHARGILY_CREDITS_10_PLAN_ID: "01k0xk3cqk4024525nh6b15wvp"
  CHARGILY_FREE_PLAN_ID: "chargily_free_plan_id"

  # Admin configuration
  ADMIN_EMAILS: "<EMAIL>,<EMAIL>,<EMAIL>"

  # Email configuration
  MAILGUN_DOMAIN: "adscloud.org"
  MAILGUN_API_URL: "https://api.eu.mailgun.net"
  SMTP_HOST: "smtp.eu.mailgun.org"
  SMTP_USERNAME: "<EMAIL>"
  SMTP_PORT: "587"
  SKIP_EMAIL_VERIFICATION_IN_DEV: "false"

  # AWS S3 configuration
  AWS_S3_FILES_BUCKET: "aws-dalti-bucket"
  AWS_S3_REGION: "eu-west-3"

  # Analytics configuration
  PLAUSIBLE_SITE_ID: "yoursite.com"
  PLAUSIBLE_BASE_URL: "https://plausible.io/api"
  GOOGLE_ANALYTICS_PROPERTY_ID: "123456789"

  # Test configuration
  TEST_API_URL: "dapi-test.adscloud.org:8443"
  TEST_USER_EMAIL: "<EMAIL>"
  OTP_TEST_MODE: "false"